<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartEstimate - Construction Estimate</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .project-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 4px solid #667eea;
        }

        .project-info h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .info-label {
            font-weight: 600;
            color: #666;
        }

        .info-value {
            color: #333;
            font-weight: 500;
        }

        .estimate-section {
            margin-bottom: 30px;
        }

        .estimate-section h3 {
            color: #333;
            font-size: 1.3em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }

        .estimate-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .estimate-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }

        .estimate-table td {
            padding: 15px;
            border-bottom: 1px solid #eee;
        }

        .estimate-table tr:hover {
            background: #f8f9fa;
        }

        .wall-type {
            font-weight: 600;
            color: #333;
        }

        .measurement {
            color: #667eea;
            font-weight: 500;
        }

        .total-row {
            background: #f0f4ff !important;
            font-weight: 600;
        }

        .total-row td {
            border-top: 2px solid #667eea;
            color: #333;
        }

        .actions {
            margin-top: 30px;
            text-align: center;
        }

        .btn {
            display: inline-block;
            padding: 12px 30px;
            margin: 0 10px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .error-message {
            background: #ffe6e6;
            color: #d00;
            padding: 20px;
            border-radius: 10px;
            margin: 20px;
            text-align: center;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .loading .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            border-top: 4px solid #667eea;
        }

        .summary-card h4 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .summary-card .value {
            font-size: 1.8em;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }

        .summary-card .unit {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Construction Estimate</h1>
            <p>Detailed analysis and calculations from your DXF file</p>
        </div>

        <div class="nav-tabs" style="display: flex; gap: 8px; margin-bottom: 24px;">
            <a href="estimate.php<?php echo isset($_GET['data']) ? '?data=' . $_GET['data'] : ''; ?>" class="nav-tab active" style="padding: 8px 16px; background: #3b82f6; color: white; border: 1px solid #3b82f6; border-radius: 8px; text-decoration: none; font-size: 14px; font-weight: 500;">Summary Estimate</a>
            <a href="cpwd_estimate.php<?php echo isset($_GET['data']) ? '?data=' . $_GET['data'] : ''; ?>" class="nav-tab" style="padding: 8px 16px; background: white; border: 1px solid #e2e8f0; border-radius: 8px; text-decoration: none; color: #64748b; font-size: 14px; font-weight: 500;">CPWD Rate Estimate</a>
        </div>

        <div class="content">
            <?php
            // Get the result data from URL parameters or session
            $resultData = null;
            
            if (isset($_GET['data'])) {
                $resultData = json_decode(base64_decode($_GET['data']), true);
            } elseif (isset($_SESSION['estimate_data'])) {
                $resultData = $_SESSION['estimate_data'];
            }
            
            if (!$resultData || !isset($resultData['success']) || !$resultData['success']) {
                echo '<div class="error-message">';
                echo '<h3>No Estimate Data Available</h3>';
                echo '<p>Please upload a DXF file first to generate an estimate.</p>';
                echo '<a href="index.php" class="btn btn-primary">Upload DXF File</a>';
                echo '</div>';
                exit;
            }
            
            // Handle nested data structure from upload response
            if (isset($resultData['data']['processing_result']['data'])) {
                $data = $resultData['data']['processing_result']['data'];
            } else {
                $data = $resultData['data'];
            }

            $wallCalculations = $data['info']['wall_calculations'] ?? [];

            // Debug: uncomment the line below to see the data structure
            // echo '<pre>DEBUG: ' . print_r($data, true) . '</pre>';
            ?>

            <!-- Project Information -->
            <div class="project-info">
                <h2>📋 Project Information</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">File Name:</span>
                        <span class="info-value"><?php echo htmlspecialchars($data['file_name'] ?? 'Unknown'); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Processed At:</span>
                        <span class="info-value"><?php echo date('Y-m-d H:i:s', strtotime($data['processed_at'] ?? 'now')); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Total Entities:</span>
                        <span class="info-value"><?php echo number_format($data['info']['total_entities'] ?? 0); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Drawing Complexity:</span>
                        <span class="info-value"><?php echo $data['analysis']['complexity'] ?? 'Unknown'; ?></span>
                    </div>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="summary-cards">
                <?php
                $totalLength = 0;
                $totalArea = 0;
                $wallCount = 0;
                
                foreach ($wallCalculations as $wallType => $calc) {
                    if ($calc['total_length'] > 0) {
                        $totalLength += $calc['total_length'];
                        $totalArea += $calc['total_area'];
                        $wallCount++;
                    }
                }
                ?>
                <div class="summary-card">
                    <h4>Total Wall Length</h4>
                    <div class="value"><?php echo number_format($totalLength, 2); ?></div>
                    <div class="unit">meters</div>
                </div>
                <div class="summary-card">
                    <h4>Total Wall Area</h4>
                    <div class="value"><?php echo number_format($totalArea, 2); ?></div>
                    <div class="unit">sq. meters</div>
                </div>
                <div class="summary-card">
                    <h4>Wall Types</h4>
                    <div class="value"><?php echo $wallCount; ?></div>
                    <div class="unit">different types</div>
                </div>
            </div>

            <!-- Excavation Estimate -->
            <div class="estimate-section">
                <h3>🏗️ Item 1: Excavation for Foundation</h3>
                <p style="margin-bottom: 20px; color: #666; font-style: italic;">
                    Excavation for foundation including labour charges, etc. complete.<br>
                    Calculations based on wall layer polygons inside GROUND_FLOOR rectangle.
                </p>
                
                <table class="estimate-table">
                    <thead>
                        <tr>
                            <th>Wall Type</th>
                            <th>Width (m)</th>
                            <th>Total Area (sq.m)</th>
                            <th>Calculated Length (m)</th>
                            <th>Rectangles Count</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $hasData = false;
                        foreach ($wallCalculations as $wallType => $calc):
                            if ($calc['total_length'] > 0):
                                $hasData = true;
                        ?>
                        <tr>
                            <td class="wall-type"><?php echo str_replace('_', ' ', $wallType); ?></td>
                            <td class="measurement"><?php echo number_format($calc['width'], 2); ?></td>
                            <td class="measurement"><?php echo number_format($calc['total_area'], 2); ?></td>
                            <td class="measurement"><?php echo number_format($calc['total_length'], 2); ?></td>
                            <td><?php echo count($calc['rectangles']); ?></td>
                        </tr>
                        <?php 
                            endif;
                        endforeach; 
                        
                        if ($hasData):
                        ?>
                        <tr class="total-row">
                            <td><strong>TOTAL</strong></td>
                            <td>-</td>
                            <td><strong><?php echo number_format($totalArea, 2); ?></strong></td>
                            <td><strong><?php echo number_format($totalLength, 2); ?></strong></td>
                            <td><strong><?php echo array_sum(array_map(function($calc) { return count($calc['rectangles']); }, $wallCalculations)); ?></strong></td>
                        </tr>
                        <?php else: ?>
                        <tr>
                            <td colspan="5" style="text-align: center; color: #666; font-style: italic;">
                                No wall layer data found in the DXF file.
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Actions -->
            <div class="actions">
                <a href="index.php" class="btn btn-secondary">Upload New File</a>
                <button onclick="window.print()" class="btn btn-primary">Print Estimate</button>
                <button onclick="downloadPDF()" class="btn btn-primary">Download PDF</button>
            </div>
        </div>
    </div>

    <script>
        function downloadPDF() {
            // This would integrate with a PDF generation library
            alert('PDF download functionality will be implemented in the next phase.');
        }
        
        // Print styles
        const printStyles = `
            @media print {
                body { background: white !important; }
                .container { box-shadow: none !important; }
                .actions { display: none !important; }
                .btn { display: none !important; }
            }
        `;
        
        const styleSheet = document.createElement("style");
        styleSheet.type = "text/css";
        styleSheet.innerText = printStyles;
        document.head.appendChild(styleSheet);
    </script>
</body>
</html>
