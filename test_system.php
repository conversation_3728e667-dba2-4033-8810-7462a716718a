<?php
/**
 * Test script to verify the SmartEstimate system is working correctly
 */

echo "<h1>SmartEstimate System Test</h1>\n";

// Test 1: Check if files exist
echo "<h2>1. File Existence Check</h2>\n";
$files = [
    'index.php' => 'Main upload page',
    'upload_handler.php' => 'Upload handler',
    'process_dxf.py' => 'Python DXF processor',
    'estimate.php' => 'Estimate results page',
    'uploads/' => 'Upload directory'
];

foreach ($files as $file => $description) {
    if (file_exists($file)) {
        echo "✅ $file ($description) - EXISTS<br>\n";
    } else {
        echo "❌ $file ($description) - MISSING<br>\n";
    }
}

// Test 2: Check Python script
echo "<h2>2. Python Script Test</h2>\n";
$testFile = 'reference files/1097. Lalitha-Estimate.dxf';
if (file_exists($testFile)) {
    echo "✅ Test DXF file exists<br>\n";
    
    // Test Python script execution
    $command = "python3 process_dxf.py " . escapeshellarg($testFile) . " 2>&1";
    $output = shell_exec($command);
    
    if ($output) {
        $result = json_decode($output, true);
        if ($result && isset($result['success']) && $result['success']) {
            echo "✅ Python script executes successfully<br>\n";
            
            // Check wall calculations
            if (isset($result['data']['info']['wall_calculations'])) {
                $wallCalcs = $result['data']['info']['wall_calculations'];
                echo "✅ Wall calculations found:<br>\n";
                foreach ($wallCalcs as $wallType => $calc) {
                    if ($calc['total_length'] > 0) {
                        echo "&nbsp;&nbsp;&nbsp;• $wallType: " . number_format($calc['total_length'], 2) . "m<br>\n";
                    }
                }
            } else {
                echo "❌ Wall calculations not found<br>\n";
            }
        } else {
            echo "❌ Python script failed: " . ($result['message'] ?? 'Unknown error') . "<br>\n";
        }
    } else {
        echo "❌ Python script execution failed<br>\n";
    }
} else {
    echo "❌ Test DXF file not found<br>\n";
}

// Test 3: Check upload directory permissions
echo "<h2>3. Upload Directory Test</h2>\n";
if (is_dir('uploads')) {
    if (is_writable('uploads')) {
        echo "✅ Upload directory is writable<br>\n";
    } else {
        echo "❌ Upload directory is not writable<br>\n";
    }
} else {
    echo "❌ Upload directory does not exist<br>\n";
}

// Test 4: Check PHP configuration
echo "<h2>4. PHP Configuration</h2>\n";
$uploadMaxFilesize = ini_get('upload_max_filesize');
$postMaxSize = ini_get('post_max_size');
$maxExecutionTime = ini_get('max_execution_time');

echo "Upload max filesize: $uploadMaxFilesize<br>\n";
echo "Post max size: $postMaxSize<br>\n";
echo "Max execution time: $maxExecutionTime seconds<br>\n";

// Test 5: Generate sample estimate URL
echo "<h2>5. Sample Estimate URL</h2>\n";
if (file_exists($testFile)) {
    $command = "python3 process_dxf.py " . escapeshellarg($testFile);
    $output = shell_exec($command);
    $result = json_decode($output, true);
    
    if ($result && $result['success']) {
        $sampleData = [
            'success' => true,
            'message' => 'File uploaded and processed successfully.',
            'data' => [
                'filename' => 'test_file.dxf',
                'original_name' => '1097. Lalitha-Estimate.dxf',
                'processing_result' => $result
            ]
        ];
        
        $encodedData = base64_encode(json_encode($sampleData));
        $estimateUrl = "http://localhost:8000/estimate.php?data=" . $encodedData;
        
        echo "✅ Sample estimate URL generated:<br>\n";
        echo "<a href='$estimateUrl' target='_blank'>View Sample Estimate</a><br>\n";
        echo "<small>URL length: " . strlen($estimateUrl) . " characters</small><br>\n";
    }
}

echo "<h2>System Status</h2>\n";
echo "🚀 SmartEstimate system is ready for use!<br>\n";
echo "<a href='index.php'>Go to Upload Page</a><br>\n";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1 { color: #333; }
h2 { color: #666; margin-top: 20px; }
a { color: #667eea; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
