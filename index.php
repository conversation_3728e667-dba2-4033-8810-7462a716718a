<?php
require_once 'project_manager.php';
$projectManager = new ProjectManager();
$projects = $projectManager->getAllProjects();
$stats = $projectManager->getProjectStats();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartEstimate - Project Management</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1a202c;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
            padding: 0 4px;
        }

        .header h1 {
            color: #1a202c;
            font-size: 32px;
            font-weight: 600;
            margin: 0;
        }

        .header p {
            color: #64748b;
            font-size: 16px;
            margin: 4px 0 0 0;
        }

        .main-section {
            max-width: 1200px;
            margin: 0 auto;
        }



        .add-btn {
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .add-btn:hover {
            background: #2563eb;
        }

        .table-container {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .table-header {
            background: #f8fafc;
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            font-size: 18px;
            font-weight: 600;
            color: #1a202c;
        }

        .table-subtitle {
            font-size: 14px;
            color: #64748b;
            margin-top: 4px;
        }

        .projects-table {
            width: 100%;
            border-collapse: collapse;
        }

        .projects-table th {
            background: #f8fafc;
            padding: 16px 24px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #e2e8f0;
        }

        .projects-table td {
            padding: 16px 24px;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
        }

        .projects-table tr:hover {
            background: #f8fafc;
        }

        .project-name {
            font-weight: 500;
            color: #1a202c;
            margin-bottom: 4px;
        }

        .project-name a {
            color: inherit;
            text-decoration: none;
        }

        .project-name a:hover {
            color: #3b82f6;
        }

        .project-details {
            font-size: 13px;
            color: #64748b;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-completed {
            background: #dcfce7;
            color: #166534;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .actions-cell {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .btn {
            padding: 6px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #4a5568;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .btn:hover {
            background: #f7fafc;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-danger {
            color: #dc2626;
            border-color: #fecaca;
        }

        .btn-danger:hover {
            background: #fecaca;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #718096;
        }

        .empty-state-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            backdrop-filter: blur(4px);
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            padding: 24px;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border: 1px solid #e2e8f0;
        }

        .modal h2 {
            font-size: 20px;
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            color: #4a5568;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .file-input-wrapper {
            position: relative;
        }

        .file-input {
            display: none;
        }

        .file-input-label {
            display: block;
            padding: 24px;
            border: 2px dashed #cbd5e1;
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8fafc;
        }

        .file-input-label:hover {
            border-color: #667eea;
            background: #edf2f7;
        }

        .file-selected {
            margin-top: 12px;
            padding: 12px;
            background: #f0fff4;
            border-radius: 8px;
            color: #38a169;
            font-size: 14px;
            display: none;
        }

        .modal-actions {
            display: flex;
            gap: 12px;
            margin-top: 24px;
        }

        .modal-actions .btn {
            flex: 1;
            padding: 12px;
            font-size: 14px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            .header {
                flex-direction: column;
                gap: 16px;
                text-align: center;
            }

            .header h1 {
                font-size: 24px;
            }

            .projects-table {
                font-size: 14px;
            }

            .projects-table th,
            .projects-table td {
                padding: 12px 16px;
            }

            .actions-cell {
                flex-direction: column;
                gap: 4px;
            }

            .btn {
                font-size: 11px;
                padding: 4px 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div>
                <h1>SmartEstimate</h1>
                <p>Professional Construction Estimation System</p>
            </div>
            <button class="add-btn" onclick="openModal('addProjectModal')">
                <span>+</span> Add New Project
            </button>
        </div>

        <div class="table-container">
            <div class="table-header">
                <div>
                    <div class="table-title">Projects</div>
                    <div class="table-subtitle"><?php echo $stats['total']; ?> total projects, <?php echo $stats['with_estimates']; ?> with estimates</div>
                </div>
                <button class="btn" onclick="loadProjects()">
                    🔄 Refresh
                </button>
            </div>

            <div id="projectsList">
                <?php if (empty($projects)): ?>
                    <div class="empty-state" style="text-align: center; padding: 60px 20px; color: #64748b;">
                        <div style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;">📋</div>
                        <h3>No Projects Yet</h3>
                        <p>Create your first project to get started with professional estimates</p>
                    </div>
                <?php else: ?>
                    <table class="projects-table">
                        <thead>
                            <tr>
                                <th>Project</th>
                                <th>R.S.No</th>
                                <th>Ward</th>
                                <th>Client</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($projects as $project): ?>
                                <tr>
                                    <td>
                                        <div class="project-name">
                                            <?php if (!empty($project['estimate_data'])): ?>
                                                <a href="cpwd_estimate.php?project=<?php echo $project['id']; ?>">
                                                    <?php echo htmlspecialchars($projectManager->generateProjectTitle($project)); ?>
                                                </a>
                                            <?php else: ?>
                                                <?php echo htmlspecialchars($projectManager->generateProjectTitle($project)); ?>
                                            <?php endif; ?>
                                        </div>
                                        <div class="project-details">
                                            <?php echo htmlspecialchars($project['project_type'] ?? 'House'); ?>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($project['rs_no'] ?: '—'); ?></td>
                                    <td><?php echo htmlspecialchars($project['ward_no'] ?: '—'); ?></td>
                                    <td><?php echo htmlspecialchars($project['client_name'] ?: '—'); ?></td>
                                    <td>
                                        <?php if (!empty($project['estimate_data'])): ?>
                                            <span class="status-badge status-completed">Completed</span>
                                        <?php else: ?>
                                            <span class="status-badge status-pending">Pending</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('M j, Y', strtotime($project['created_at'])); ?></td>
                                    <td>
                                        <div class="actions-cell">
                                            <?php if (!empty($project['estimate_data'])): ?>
                                                <a href="cpwd_estimate.php?project=<?php echo $project['id']; ?>" class="btn btn-primary">
                                                    View
                                                </a>
                                                <button class="btn" onclick="editRates('<?php echo $project['id']; ?>')">
                                                    Rates
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-primary" onclick="uploadDxfForProject('<?php echo $project['id']; ?>')">
                                                    Upload
                                                </button>
                                            <?php endif; ?>
                                            <button class="btn" onclick="editProject('<?php echo $project['id']; ?>', <?php echo htmlspecialchars(json_encode($project)); ?>)">
                                                Edit
                                            </button>
                                            <button class="btn btn-danger" onclick="deleteProject('<?php echo $project['id']; ?>')">
                                                Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add New Project Modal -->
    <div id="addProjectModal" class="modal">
        <div class="modal-content">
            <h2>Add New Project</h2>
            <form id="projectForm">
                <div class="form-group">
                    <label for="rs_no">R.S.No:</label>
                    <input type="text" id="rs_no" name="rs_no" placeholder="e.g., 116/3A1A2PT3">
                </div>

                <div class="form-group">
                    <label for="ward_no">Ward No:</label>
                    <input type="text" id="ward_no" name="ward_no" placeholder="e.g., 3">
                </div>

                <div class="form-group">
                    <label for="panchayath">Panchayath:</label>
                    <input type="text" id="panchayath" name="panchayath" placeholder="e.g., KUMBLA GRAMA PANCHAYATH">
                </div>

                <div class="form-group">
                    <label for="client_name">Client Name:</label>
                    <input type="text" id="client_name" name="client_name" placeholder="e.g., JOHN DOE">
                </div>



                <div class="form-group">
                    <label for="dxf_file">DXF File: *</label>
                    <div class="file-input-wrapper">
                        <input type="file" id="dxfFile" name="dxf_file" class="file-input" accept=".dxf" required>
                        <label for="dxfFile" class="file-input-label" id="fileLabel">
                            <div style="font-size: 24px; margin-bottom: 8px;">📁</div>
                            <div>Click to select DXF file or drag and drop here</div>
                        </label>
                        <div class="file-selected" id="fileSelected"></div>
                    </div>
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn" onclick="closeModal('addProjectModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="createProjectBtn" disabled>Create Project</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Project Modal -->
    <div id="editProjectModal" class="modal">
        <div class="modal-content">
            <h2>Edit Project</h2>
            <form id="editProjectForm">
                <input type="hidden" id="editProjectId" name="project_id">

                <div class="form-group">
                    <label for="edit_rs_no">R.S.No:</label>
                    <input type="text" id="edit_rs_no" name="rs_no" placeholder="e.g., 116/3A1A2PT3">
                </div>

                <div class="form-group">
                    <label for="edit_ward_no">Ward No:</label>
                    <input type="text" id="edit_ward_no" name="ward_no" placeholder="e.g., 3">
                </div>

                <div class="form-group">
                    <label for="edit_panchayath">Panchayath:</label>
                    <input type="text" id="edit_panchayath" name="panchayath" placeholder="e.g., KUMBLA GRAMA PANCHAYATH">
                </div>

                <div class="form-group">
                    <label for="edit_client_name">Client Name:</label>
                    <input type="text" id="edit_client_name" name="client_name" placeholder="e.g., JOHN DOE">
                </div>



                <div class="modal-actions">
                    <button type="button" class="btn" onclick="closeModal('editProjectModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Project</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Upload DXF Modal -->
    <div id="uploadDxfModal" class="modal">
        <div class="modal-content">
            <h2>Upload DXF File</h2>
            <form id="uploadDxfForm">
                <input type="hidden" id="uploadProjectId" name="project_id">

                <div class="form-group">
                    <label for="upload_dxf_file">DXF File: *</label>
                    <div class="file-input-wrapper">
                        <input type="file" id="upload_dxf_file" name="dxf_file" class="file-input" accept=".dxf" required>
                        <label for="upload_dxf_file" class="file-input-label" id="uploadFileLabel">
                            <div style="font-size: 24px; margin-bottom: 8px;">📁</div>
                            <div>Click to select DXF file or drag and drop here</div>
                        </label>
                        <div class="file-selected" id="uploadFileSelected"></div>
                    </div>
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn" onclick="closeModal('uploadDxfModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="uploadDxfBtn" disabled>Upload & Generate</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Project form submission
        document.getElementById('projectForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const file = formData.get('dxf_file');

            if (!file || file.size === 0) {
                alert('Please select a DXF file.');
                return;
            }

            const projectData = {
                action: 'create',
                rs_no: formData.get('rs_no'),
                ward_no: formData.get('ward_no'),
                panchayath: formData.get('panchayath'),
                client_name: formData.get('client_name'),
                project_type: 'HOUSE' // Default project type
            };

            try {
                const createBtn = document.getElementById('createProjectBtn');
                createBtn.textContent = 'Creating...';
                createBtn.disabled = true;

                const response = await fetch('project_manager.php?api=projects', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(projectData)
                });

                const result = await response.json();

                if (result.success) {
                    createBtn.textContent = 'Uploading...';

                    const uploadFormData = new FormData();
                    uploadFormData.append('dxf_file', file);
                    uploadFormData.append('project_id', result.project.id);

                    const uploadResponse = await fetch('upload_handler.php', {
                        method: 'POST',
                        body: uploadFormData
                    });

                    const uploadResult = await uploadResponse.json();

                    if (uploadResult.success) {
                        window.location.href = `cpwd_estimate.php?project=${result.project.id}`;
                    } else {
                        alert('Upload failed: ' + uploadResult.error);
                        createBtn.textContent = 'Create Project';
                        createBtn.disabled = false;
                    }
                } else {
                    alert('Error creating project: ' + result.error);
                    createBtn.textContent = 'Create Project';
                    createBtn.disabled = false;
                }
            } catch (error) {
                alert('Error: ' + error.message);
                createBtn.textContent = 'Create Project';
                createBtn.disabled = false;
            }
        });

        // Edit project form submission
        document.getElementById('editProjectForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const projectData = {
                action: 'update',
                id: formData.get('project_id'),
                rs_no: formData.get('rs_no'),
                ward_no: formData.get('ward_no'),
                panchayath: formData.get('panchayath'),
                client_name: formData.get('client_name'),
                project_type: 'HOUSE' // Default project type
            };

            try {
                const response = await fetch('project_manager.php?api=projects', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(projectData)
                });

                const result = await response.json();

                if (result.success) {
                    closeModal('editProjectModal');
                    location.reload();
                } else {
                    alert('Error updating project: ' + result.error);
                }
            } catch (error) {
                alert('Error updating project: ' + error.message);
            }
        });

        // Upload DXF form submission
        document.getElementById('uploadDxfForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const file = formData.get('dxf_file');
            const projectId = formData.get('project_id');

            if (!file || file.size === 0) {
                alert('Please select a DXF file.');
                return;
            }

            try {
                const uploadBtn = document.getElementById('uploadDxfBtn');
                uploadBtn.textContent = 'Uploading...';
                uploadBtn.disabled = true;

                const uploadFormData = new FormData();
                uploadFormData.append('dxf_file', file);
                uploadFormData.append('project_id', projectId);

                const response = await fetch('upload_handler.php', {
                    method: 'POST',
                    body: uploadFormData
                });

                const result = await response.json();

                if (result.success) {
                    closeModal('uploadDxfModal');
                    window.location.href = `cpwd_estimate.php?project=${projectId}`;
                } else {
                    alert('Upload failed: ' + result.error);
                    uploadBtn.textContent = 'Upload & Generate';
                    uploadBtn.disabled = false;
                }
            } catch (error) {
                alert('Upload failed: ' + error.message);
                uploadBtn.textContent = 'Upload & Generate';
                uploadBtn.disabled = false;
            }
        });

        // File handling for add project modal
        const fileInput = document.getElementById('dxfFile');
        const fileSelected = document.getElementById('fileSelected');
        const createBtn = document.getElementById('createProjectBtn');

        // File handling for upload DXF modal
        const uploadFileInput = document.getElementById('upload_dxf_file');
        const uploadFileSelected = document.getElementById('uploadFileSelected');
        const uploadDxfBtn = document.getElementById('uploadDxfBtn');

        fileInput.addEventListener('change', function(e) {
            handleFileSelect(e.target.files[0]);
        });

        uploadFileInput.addEventListener('change', function(e) {
            handleUploadFileSelect(e.target.files[0]);
        });

        function handleFileSelect(file) {
            if (!file) {
                fileSelected.style.display = 'none';
                createBtn.disabled = true;
                return;
            }

            if (!file.name.toLowerCase().endsWith('.dxf')) {
                alert('Please select a valid DXF file.');
                fileSelected.style.display = 'none';
                createBtn.disabled = true;
                return;
            }

            if (file.size > 50 * 1024 * 1024) {
                alert('File size must be less than 50MB.');
                fileSelected.style.display = 'none';
                createBtn.disabled = true;
                return;
            }

            fileSelected.innerHTML = `<strong>Selected:</strong> ${file.name}`;
            fileSelected.style.display = 'block';
            createBtn.disabled = false;
        }

        function handleUploadFileSelect(file) {
            if (!file) {
                uploadFileSelected.style.display = 'none';
                uploadDxfBtn.disabled = true;
                return;
            }

            if (!file.name.toLowerCase().endsWith('.dxf')) {
                alert('Please select a valid DXF file.');
                uploadFileSelected.style.display = 'none';
                uploadDxfBtn.disabled = true;
                return;
            }

            if (file.size > 50 * 1024 * 1024) {
                alert('File size must be less than 50MB.');
                uploadFileSelected.style.display = 'none';
                uploadDxfBtn.disabled = true;
                return;
            }

            uploadFileSelected.innerHTML = `<strong>Selected:</strong> ${file.name}`;
            uploadFileSelected.style.display = 'block';
            uploadDxfBtn.disabled = false;
        }

        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function editProject(projectId, projectData) {
            document.getElementById('editProjectId').value = projectId;
            document.getElementById('edit_rs_no').value = projectData.rs_no || '';
            document.getElementById('edit_ward_no').value = projectData.ward_no || '';
            document.getElementById('edit_panchayath').value = projectData.panchayath || '';
            document.getElementById('edit_client_name').value = projectData.client_name || '';
            openModal('editProjectModal');
        }

        function uploadDxfForProject(projectId) {
            document.getElementById('uploadProjectId').value = projectId;
            openModal('uploadDxfModal');
        }

        function editRates(projectId) {
            window.location.href = `cpwd_estimate.php?project=${projectId}&edit_rates=1`;
        }

        async function deleteProject(projectId) {
            if (!confirm('Are you sure you want to delete this project?')) return;

            try {
                const response = await fetch('project_manager.php?api=projects', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'delete', id: projectId })
                });

                const result = await response.json();
                if (result.success) {
                    location.reload();
                } else {
                    alert('Error deleting project: ' + result.error);
                }
            } catch (error) {
                alert('Error deleting project: ' + error.message);
            }
        }

        function loadProjects() {
            location.reload();
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }
    </script>
</body>
</html>
