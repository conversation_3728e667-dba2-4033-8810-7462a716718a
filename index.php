<?php
require_once 'project_manager.php';
$projectManager = new ProjectManager();
$projects = $projectManager->getAllProjects();
$stats = $projectManager->getProjectStats();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartEstimate - Project Management</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            padding: 20px;
            color: #1e293b;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            padding: 32px;
            margin-bottom: 24px;
            text-align: center;
        }

        .header h1 {
            color: #0f172a;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .header p {
            color: #64748b;
            font-size: 16px;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            padding: 20px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #3b82f6;
            margin-bottom: 4px;
        }

        .stat-label {
            color: #64748b;
            font-size: 14px;
        }

        .main-content {
            display: grid;
            grid-template-columns: 400px 1fr;
            gap: 24px;
        }

        .project-form {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            padding: 24px;
            height: fit-content;
        }

        .project-form h2 {
            color: #0f172a;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            color: #374151;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 6px;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .btn {
            padding: 10px 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            color: #374151;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            background: #f9fafb;
            border-color: #d1d5db;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-full {
            width: 100%;
            justify-content: center;
        }

        .projects-section {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            padding: 24px;
        }

        .projects-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .projects-header h2 {
            color: #0f172a;
            font-size: 20px;
            font-weight: 600;
        }

        .project-card {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            transition: all 0.2s;
        }

        .project-card:hover {
            border-color: #3b82f6;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .project-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .project-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 8px;
            margin-bottom: 12px;
            font-size: 13px;
            color: #64748b;
        }

        .project-actions {
            display: flex;
            gap: 8px;
        }

        .project-actions .btn {
            padding: 6px 12px;
            font-size: 12px;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #64748b;
        }

        .file-upload-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }

        .file-input-wrapper {
            position: relative;
            margin-bottom: 16px;
        }

        .file-input {
            display: none;
        }

        .file-input-label {
            display: block;
            padding: 20px;
            border: 2px dashed #cbd5e1;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #f8fafc;
        }

        .file-input-label:hover {
            border-color: #3b82f6;
            background: #f1f5f9;
        }

        .file-input-label.dragover {
            border-color: #3b82f6;
            background: #dbeafe;
        }

        .file-selected {
            margin-top: 10px;
            padding: 10px;
            background: #e8f5e8;
            border-radius: 5px;
            color: #2d5a2d;
            display: none;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            padding: 24px;
            max-width: 500px;
            width: 90%;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>SmartEstimate</h1>
            <p>Construction Estimation & Project Management System</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total']; ?></div>
                <div class="stat-label">Total Projects</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['with_estimates']; ?></div>
                <div class="stat-label">With Estimates</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['recent']; ?></div>
                <div class="stat-label">Recent (7 days)</div>
            </div>
        </div>

        <div class="main-content">
            <div class="project-form">
                <h2>Add New Project</h2>
                <form id="projectForm">
                    <div class="form-group">
                        <label for="rs_no">R.S.No:</label>
                        <input type="text" id="rs_no" name="rs_no" placeholder="e.g., 116/3A1A2PT3">
                    </div>
                    
                    <div class="form-group">
                        <label for="ward_no">Ward No:</label>
                        <input type="text" id="ward_no" name="ward_no" placeholder="e.g., 3">
                    </div>
                    
                    <div class="form-group">
                        <label for="panchayath">Panchayath:</label>
                        <input type="text" id="panchayath" name="panchayath" placeholder="e.g., KUMBLA GRAMA PANCHAYATH">
                    </div>
                    
                    <div class="form-group">
                        <label for="client_name">Client Name:</label>
                        <input type="text" id="client_name" name="client_name" placeholder="e.g., JOHN DOE">
                    </div>
                    
                    <div class="form-group">
                        <label for="project_type">Project Type:</label>
                        <select id="project_type" name="project_type">
                            <option value="HOUSE">House</option>
                            <option value="COMMERCIAL BUILDING">Commercial Building</option>
                            <option value="APARTMENT">Apartment</option>
                            <option value="OFFICE">Office</option>
                            <option value="WAREHOUSE">Warehouse</option>
                            <option value="OTHER">Other</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="dxf_file">DXF File: *</label>
                        <div class="file-input-wrapper">
                            <input type="file" id="dxfFile" name="dxf_file" class="file-input" accept=".dxf" required>
                            <label for="dxfFile" class="file-input-label" id="fileLabel">
                                <div style="font-size: 24px; margin-bottom: 8px;">📁</div>
                                <div>Click to select DXF file or drag and drop here</div>
                            </label>
                            <div class="file-selected" id="fileSelected"></div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary btn-full" id="createProjectBtn" disabled>Create Project & Generate Estimate</button>
                </form>
                    <h3 style="margin-bottom: 16px; color: #374151;">Upload DXF File</h3>
                    <div class="file-input-wrapper">
                        <input type="file" id="dxfFile" name="dxf_file" class="file-input" accept=".dxf" required>
                        <label for="dxfFile" class="file-input-label" id="fileLabel">
                            <div style="font-size: 24px; margin-bottom: 8px;">📁</div>
                            <div>Click to select DXF file or drag and drop here</div>
                        </label>
                        <div class="file-selected" id="fileSelected"></div>
                    </div>
                    <button type="button" class="btn btn-primary btn-full" id="uploadBtn" disabled>
                        Upload & Generate Estimate
                    </button>
                </div>
            </div>

            <div class="projects-section">
                <div class="projects-header">
                    <h2>Project History</h2>
                    <button class="btn" onclick="loadProjects()">
                        <span>🔄</span> Refresh
                    </button>
                </div>

                <div id="projectsList">
                    <?php if (empty($projects)): ?>
                        <div class="empty-state">
                            <div style="font-size: 48px; margin-bottom: 16px;">📋</div>
                            <h3>No Projects Yet</h3>
                            <p>Create your first project to get started with estimates</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($projects as $project): ?>
                            <div class="project-card">
                                <div class="project-title">
                                    <?php if (!empty($project['estimate_data'])): ?>
                                        <a href="cpwd_estimate.php?project=<?php echo $project['id']; ?>" style="text-decoration: none; color: inherit;">
                                            <?php echo htmlspecialchars($projectManager->generateProjectTitle($project)); ?>
                                        </a>
                                    <?php else: ?>
                                        <?php echo htmlspecialchars($projectManager->generateProjectTitle($project)); ?>
                                    <?php endif; ?>
                                </div>
                                <div class="project-details">
                                    <div><strong>R.S.No:</strong> <?php echo htmlspecialchars($project['rs_no'] ?: 'Not specified'); ?></div>
                                    <div><strong>Ward:</strong> <?php echo htmlspecialchars($project['ward_no'] ?: 'Not specified'); ?></div>
                                    <div><strong>Client:</strong> <?php echo htmlspecialchars($project['client_name'] ?: 'Not specified'); ?></div>
                                    <div><strong>Created:</strong> <?php echo date('M j, Y', strtotime($project['created_at'])); ?></div>
                                </div>
                                <div class="project-actions">
                                    <?php if (!empty($project['estimate_data'])): ?>
                                        <a href="cpwd_estimate.php?project=<?php echo $project['id']; ?>" class="btn btn-primary">
                                            View Estimate
                                        </a>
                                    <?php else: ?>
                                        <button class="btn" onclick="selectProject('<?php echo $project['id']; ?>')">
                                            Add DXF File
                                        </button>
                                    <?php endif; ?>
                                    <button class="btn" onclick="editProject('<?php echo $project['id']; ?>')">
                                        Edit
                                    </button>
                                    <button class="btn" onclick="deleteProject('<?php echo $project['id']; ?>')" style="color: #dc2626;">
                                        Delete
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentProjectId = null;

        // Project form submission
        document.getElementById('projectForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const file = formData.get('dxf_file');

            if (!file || file.size === 0) {
                alert('Please select a DXF file.');
                return;
            }

            // First create the project
            const projectData = {
                action: 'create',
                rs_no: formData.get('rs_no'),
                ward_no: formData.get('ward_no'),
                panchayath: formData.get('panchayath'),
                client_name: formData.get('client_name'),
                project_type: formData.get('project_type')
            };

            try {
                const createBtn = document.getElementById('createProjectBtn');
                createBtn.textContent = 'Creating Project...';
                createBtn.disabled = true;

                const response = await fetch('project_manager.php?api=projects', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(projectData)
                });

                const result = await response.json();

                if (result.success) {
                    // Now upload the DXF file
                    createBtn.textContent = 'Uploading DXF File...';

                    const uploadFormData = new FormData();
                    uploadFormData.append('dxf_file', file);
                    uploadFormData.append('project_id', result.project.id);

                    const uploadResponse = await fetch('upload_handler.php', {
                        method: 'POST',
                        body: uploadFormData
                    });

                    const uploadResult = await uploadResponse.json();

                    if (uploadResult.success) {
                        // Redirect to estimate page
                        window.location.href = `cpwd_estimate.php?project=${result.project.id}`;
                    } else {
                        alert('Upload failed: ' + uploadResult.error);
                        createBtn.textContent = 'Create Project & Generate Estimate';
                        createBtn.disabled = false;
                    }
                } else {
                    alert('Error creating project: ' + result.error);
                    createBtn.textContent = 'Create Project & Generate Estimate';
                    createBtn.disabled = false;
                }
            } catch (error) {
                alert('Error: ' + error.message);
                createBtn.textContent = 'Create Project & Generate Estimate';
                createBtn.disabled = false;
            }
        });

        // File upload handling
        const fileInput = document.getElementById('dxfFile');
        const fileLabel = document.getElementById('fileLabel');
        const fileSelected = document.getElementById('fileSelected');
        const createBtn = document.getElementById('createProjectBtn');

        fileInput.addEventListener('change', function(e) {
            handleFileSelect(e.target.files[0]);
        });

        // Drag and drop handlers
        fileLabel.addEventListener('dragover', function(e) {
            e.preventDefault();
            fileLabel.classList.add('dragover');
        });

        fileLabel.addEventListener('dragleave', function(e) {
            e.preventDefault();
            fileLabel.classList.remove('dragover');
        });

        fileLabel.addEventListener('drop', function(e) {
            e.preventDefault();
            fileLabel.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect(files[0]);
            }
        });

        function handleFileSelect(file) {
            if (!file) {
                fileSelected.style.display = 'none';
                createBtn.disabled = true;
                return;
            }

            if (!file.name.toLowerCase().endsWith('.dxf')) {
                alert('Please select a valid DXF file.');
                fileSelected.style.display = 'none';
                createBtn.disabled = true;
                return;
            }

            // Validate file size (50MB limit)
            if (file.size > 50 * 1024 * 1024) {
                alert('File size must be less than 50MB.');
                fileSelected.style.display = 'none';
                createBtn.disabled = true;
                return;
            }

            fileSelected.innerHTML = `<strong>Selected:</strong> ${file.name} (${formatFileSize(file.size)})`;
            fileSelected.style.display = 'block';
            createBtn.disabled = false;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }



        function editProject(projectId) {
            // Implementation for editing project
            alert('Edit functionality will be implemented');
        }

        async function deleteProject(projectId) {
            if (!confirm('Are you sure you want to delete this project?')) {
                return;
            }

            try {
                const response = await fetch('project_manager.php?api=projects', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'delete',
                        id: projectId
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    loadProjects();
                } else {
                    alert('Error deleting project: ' + result.error);
                }
            } catch (error) {
                alert('Error deleting project: ' + error.message);
            }
        }

        async function loadProjects() {
            try {
                const response = await fetch('project_manager.php?api=projects');
                const result = await response.json();
                
                if (result.success) {
                    // Update stats
                    document.querySelector('.stat-card:nth-child(1) .stat-number').textContent = result.stats.total;
                    document.querySelector('.stat-card:nth-child(2) .stat-number').textContent = result.stats.with_estimates;
                    document.querySelector('.stat-card:nth-child(3) .stat-number').textContent = result.stats.recent;
                    
                    // Reload page to update projects list
                    location.reload();
                }
            } catch (error) {
                console.error('Error loading projects:', error);
            }
        }
    </script>
</body>
</html>
