<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartEstimate - DXF File Upload</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .upload-form {
            margin-bottom: 20px;
        }

        .file-input-wrapper {
            position: relative;
            margin-bottom: 20px;
        }

        .file-input {
            display: none;
        }

        .file-input-label {
            display: block;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f9f9f9;
        }

        .file-input-label:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .file-input-label.dragover {
            border-color: #667eea;
            background: #e8f0ff;
        }

        .file-icon {
            font-size: 3em;
            color: #ddd;
            margin-bottom: 10px;
        }

        .file-text {
            color: #666;
            font-size: 1.1em;
        }

        .file-selected {
            margin-top: 10px;
            padding: 10px;
            background: #e8f5e8;
            border-radius: 5px;
            color: #2d5a2d;
            display: none;
        }

        .submit-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .submit-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .submit-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .progress-container {
            display: none;
            margin-bottom: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            color: #666;
        }

        .error-message {
            background: #ffe6e6;
            color: #d00;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: none;
        }

        .success-message {
            background: #e6ffe6;
            color: #0a5d0a;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: none;
        }

        .file-requirements {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }

        .file-requirements h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .file-requirements ul {
            color: #666;
            padding-left: 20px;
        }

        .file-requirements li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>SmartEstimate</h1>
            <p>Upload your DXF file for processing</p>
        </div>

        <div class="file-requirements">
            <h3>File Requirements:</h3>
            <ul>
                <li>Only .dxf files are accepted</li>
                <li>Maximum file size: 50MB</li>
                <li>File must be a valid AutoCAD DXF format</li>
            </ul>
        </div>

        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>

        <form id="uploadForm" class="upload-form" enctype="multipart/form-data">
            <div class="file-input-wrapper">
                <input type="file" id="dxfFile" name="dxf_file" class="file-input" accept=".dxf" required>
                <label for="dxfFile" class="file-input-label" id="fileLabel">
                    <div class="file-icon">📁</div>
                    <div class="file-text">
                        Click to select a DXF file or drag and drop here
                    </div>
                </label>
                <div class="file-selected" id="fileSelected"></div>
            </div>

            <div class="progress-container" id="progressContainer">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">Uploading... 0%</div>
            </div>

            <button type="submit" class="submit-btn" id="submitBtn">
                Process DXF File
            </button>
        </form>
    </div>

    <script>
        const form = document.getElementById('uploadForm');
        const fileInput = document.getElementById('dxfFile');
        const fileLabel = document.getElementById('fileLabel');
        const fileSelected = document.getElementById('fileSelected');
        const submitBtn = document.getElementById('submitBtn');
        const progressContainer = document.getElementById('progressContainer');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');

        // File input change handler
        fileInput.addEventListener('change', function(e) {
            handleFileSelect(e.target.files[0]);
        });

        // Drag and drop handlers
        fileLabel.addEventListener('dragover', function(e) {
            e.preventDefault();
            fileLabel.classList.add('dragover');
        });

        fileLabel.addEventListener('dragleave', function(e) {
            e.preventDefault();
            fileLabel.classList.remove('dragover');
        });

        fileLabel.addEventListener('drop', function(e) {
            e.preventDefault();
            fileLabel.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect(files[0]);
            }
        });

        function handleFileSelect(file) {
            hideMessages();
            
            if (!file) {
                fileSelected.style.display = 'none';
                submitBtn.disabled = true;
                return;
            }

            // Validate file type
            if (!file.name.toLowerCase().endsWith('.dxf')) {
                showError('Please select a valid DXF file.');
                fileSelected.style.display = 'none';
                submitBtn.disabled = true;
                return;
            }

            // Validate file size (50MB limit)
            if (file.size > 50 * 1024 * 1024) {
                showError('File size must be less than 50MB.');
                fileSelected.style.display = 'none';
                submitBtn.disabled = true;
                return;
            }

            // Show selected file
            fileSelected.innerHTML = `
                <strong>Selected:</strong> ${file.name} 
                <span style="color: #666;">(${formatFileSize(file.size)})</span>
            `;
            fileSelected.style.display = 'block';
            submitBtn.disabled = false;
        }

        // Form submission handler
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const file = fileInput.files[0];
            if (!file) {
                showError('Please select a DXF file.');
                return;
            }

            uploadFile(file);
        });

        function uploadFile(file) {
            const formData = new FormData();
            formData.append('dxf_file', file);

            // Show progress
            progressContainer.style.display = 'block';
            submitBtn.disabled = true;
            hideMessages();

            // Create XMLHttpRequest for progress tracking
            const xhr = new XMLHttpRequest();

            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    progressFill.style.width = percentComplete + '%';
                    progressText.textContent = `Uploading... ${Math.round(percentComplete)}%`;
                }
            });

            xhr.addEventListener('load', function() {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            // Encode the response data and redirect to estimate page
                            const encodedData = btoa(JSON.stringify(response));
                            window.location.href = `estimate.php?data=${encodedData}`;
                        } else {
                            showError(response.error || 'An error occurred while processing the file.');
                            progressContainer.style.display = 'none';
                            submitBtn.disabled = false;
                        }
                    } catch (e) {
                        showError('Invalid response from server.');
                        progressContainer.style.display = 'none';
                        submitBtn.disabled = false;
                    }
                } else {
                    showError('Upload failed. Please try again.');
                    progressContainer.style.display = 'none';
                    submitBtn.disabled = false;
                }
            });

            xhr.addEventListener('error', function() {
                showError('Upload failed. Please check your connection and try again.');
                progressContainer.style.display = 'none';
                submitBtn.disabled = false;
            });

            xhr.open('POST', 'upload_handler.php');
            xhr.send(formData);
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            successMessage.style.display = 'none';
        }

        function showSuccess(message) {
            successMessage.textContent = message;
            successMessage.style.display = 'block';
            errorMessage.style.display = 'none';
        }

        function hideMessages() {
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
        }
    </script>
</body>
</html>
