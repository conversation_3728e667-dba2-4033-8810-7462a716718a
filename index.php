<?php
require_once 'project_manager.php';
$projectManager = new ProjectManager();
$projects = $projectManager->getAllProjects();
$stats = $projectManager->getProjectStats();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartEstimate - Project Management</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #1a202c;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
        }

        .header h1 {
            color: white;
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 16px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 20px;
            font-weight: 400;
        }

        .main-section {
            display: grid;
            grid-template-columns: 400px 1fr;
            gap: 40px;
            align-items: start;
        }

        .add-project-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .add-project-card h2 {
            font-size: 24px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 16px;
        }

        .add-project-card p {
            color: #718096;
            margin-bottom: 32px;
            line-height: 1.6;
        }

        .add-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 16px 32px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
            width: 100%;
        }

        .add-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 24px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #718096;
            font-size: 14px;
            font-weight: 500;
        }

        .projects-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 32px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .projects-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .projects-header h2 {
            font-size: 24px;
            font-weight: 600;
            color: #2d3748;
        }

        .refresh-btn {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 8px 16px;
            color: #4a5568;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .refresh-btn:hover {
            background: #edf2f7;
        }

        .project-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            transition: all 0.3s ease;
        }

        .project-card:hover {
            border-color: #667eea;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
            transform: translateY(-2px);
        }

        .project-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 12px;
            font-size: 16px;
            line-height: 1.4;
        }

        .project-title a {
            color: inherit;
            text-decoration: none;
        }

        .project-title a:hover {
            color: #667eea;
        }

        .project-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 12px;
            margin-bottom: 16px;
            font-size: 13px;
        }

        .project-meta-item {
            color: #718096;
        }

        .project-meta-item strong {
            color: #4a5568;
        }

        .project-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 6px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #4a5568;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .btn:hover {
            background: #f7fafc;
        }

        .btn-primary {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .btn-primary:hover {
            background: #5a67d8;
        }

        .btn-danger {
            color: #e53e3e;
            border-color: #fed7d7;
        }

        .btn-danger:hover {
            background: #fed7d7;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #718096;
        }

        .empty-state-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            backdrop-filter: blur(4px);
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 20px;
            padding: 32px;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
        }

        .modal h2 {
            font-size: 24px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 24px;
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            color: #4a5568;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .file-input-wrapper {
            position: relative;
        }

        .file-input {
            display: none;
        }

        .file-input-label {
            display: block;
            padding: 24px;
            border: 2px dashed #cbd5e1;
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8fafc;
        }

        .file-input-label:hover {
            border-color: #667eea;
            background: #edf2f7;
        }

        .file-selected {
            margin-top: 12px;
            padding: 12px;
            background: #f0fff4;
            border-radius: 8px;
            color: #38a169;
            font-size: 14px;
            display: none;
        }

        .modal-actions {
            display: flex;
            gap: 12px;
            margin-top: 24px;
        }

        .modal-actions .btn {
            flex: 1;
            padding: 12px;
            font-size: 14px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .main-section {
                grid-template-columns: 1fr;
                gap: 24px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .header h1 {
                font-size: 36px;
            }
            
            .header p {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>SmartEstimate</h1>
            <p>Professional Construction Estimation System</p>
        </div>

        <div class="main-section">
            <div class="add-project-card">
                <h2>Create New Project</h2>
                <p>Start by creating a new construction project with your DXF file to generate professional estimates.</p>
                <button class="add-btn" onclick="openModal('addProjectModal')">
                    📋 Add New Project
                </button>
            </div>

            <div>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['total']; ?></div>
                        <div class="stat-label">Total Projects</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['with_estimates']; ?></div>
                        <div class="stat-label">With Estimates</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['recent']; ?></div>
                        <div class="stat-label">Recent (7 days)</div>
                    </div>
                </div>

                <div class="projects-section">
                    <div class="projects-header">
                        <h2>Recent Projects</h2>
                        <button class="refresh-btn" onclick="loadProjects()">
                            🔄 Refresh
                        </button>
                    </div>

                    <div id="projectsList">
                        <?php if (empty($projects)): ?>
                            <div class="empty-state">
                                <div class="empty-state-icon">📋</div>
                                <h3>No Projects Yet</h3>
                                <p>Create your first project to get started with professional estimates</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($projects as $project): ?>
                                <div class="project-card">
                                    <div class="project-title">
                                        <?php if (!empty($project['estimate_data'])): ?>
                                            <a href="cpwd_estimate.php?project=<?php echo $project['id']; ?>">
                                                <?php echo htmlspecialchars($projectManager->generateProjectTitle($project)); ?>
                                            </a>
                                        <?php else: ?>
                                            <?php echo htmlspecialchars($projectManager->generateProjectTitle($project)); ?>
                                        <?php endif; ?>
                                    </div>
                                    <div class="project-meta">
                                        <div class="project-meta-item"><strong>R.S.No:</strong> <?php echo htmlspecialchars($project['rs_no'] ?: 'Not specified'); ?></div>
                                        <div class="project-meta-item"><strong>Ward:</strong> <?php echo htmlspecialchars($project['ward_no'] ?: 'Not specified'); ?></div>
                                        <div class="project-meta-item"><strong>Client:</strong> <?php echo htmlspecialchars($project['client_name'] ?: 'Not specified'); ?></div>
                                        <div class="project-meta-item"><strong>Created:</strong> <?php echo date('M j, Y', strtotime($project['created_at'])); ?></div>
                                    </div>
                                    <div class="project-actions">
                                        <?php if (!empty($project['estimate_data'])): ?>
                                            <a href="cpwd_estimate.php?project=<?php echo $project['id']; ?>" class="btn btn-primary">
                                                View Estimate
                                            </a>
                                            <button class="btn" onclick="editRates('<?php echo $project['id']; ?>')">
                                                Edit Rates
                                            </button>
                                        <?php else: ?>
                                            <button class="btn btn-primary" onclick="uploadDxfForProject('<?php echo $project['id']; ?>')">
                                                Add DXF File
                                            </button>
                                        <?php endif; ?>
                                        <button class="btn" onclick="editProject('<?php echo $project['id']; ?>', <?php echo htmlspecialchars(json_encode($project)); ?>)">
                                            Edit
                                        </button>
                                        <button class="btn btn-danger" onclick="deleteProject('<?php echo $project['id']; ?>')">
                                            Delete
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add New Project Modal -->
    <div id="addProjectModal" class="modal">
        <div class="modal-content">
            <h2>Add New Project</h2>
            <form id="projectForm">
                <div class="form-group">
                    <label for="rs_no">R.S.No:</label>
                    <input type="text" id="rs_no" name="rs_no" placeholder="e.g., 116/3A1A2PT3">
                </div>

                <div class="form-group">
                    <label for="ward_no">Ward No:</label>
                    <input type="text" id="ward_no" name="ward_no" placeholder="e.g., 3">
                </div>

                <div class="form-group">
                    <label for="panchayath">Panchayath:</label>
                    <input type="text" id="panchayath" name="panchayath" placeholder="e.g., KUMBLA GRAMA PANCHAYATH">
                </div>

                <div class="form-group">
                    <label for="client_name">Client Name:</label>
                    <input type="text" id="client_name" name="client_name" placeholder="e.g., JOHN DOE">
                </div>

                <div class="form-group">
                    <label for="project_type">Project Type:</label>
                    <select id="project_type" name="project_type">
                        <option value="HOUSE">House</option>
                        <option value="COMMERCIAL BUILDING">Commercial Building</option>
                        <option value="APARTMENT">Apartment</option>
                        <option value="OFFICE">Office</option>
                        <option value="WAREHOUSE">Warehouse</option>
                        <option value="OTHER">Other</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="dxf_file">DXF File: *</label>
                    <div class="file-input-wrapper">
                        <input type="file" id="dxfFile" name="dxf_file" class="file-input" accept=".dxf" required>
                        <label for="dxfFile" class="file-input-label" id="fileLabel">
                            <div style="font-size: 24px; margin-bottom: 8px;">📁</div>
                            <div>Click to select DXF file or drag and drop here</div>
                        </label>
                        <div class="file-selected" id="fileSelected"></div>
                    </div>
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn" onclick="closeModal('addProjectModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="createProjectBtn" disabled>Create Project</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Project Modal -->
    <div id="editProjectModal" class="modal">
        <div class="modal-content">
            <h2>Edit Project</h2>
            <form id="editProjectForm">
                <input type="hidden" id="editProjectId" name="project_id">

                <div class="form-group">
                    <label for="edit_rs_no">R.S.No:</label>
                    <input type="text" id="edit_rs_no" name="rs_no" placeholder="e.g., 116/3A1A2PT3">
                </div>

                <div class="form-group">
                    <label for="edit_ward_no">Ward No:</label>
                    <input type="text" id="edit_ward_no" name="ward_no" placeholder="e.g., 3">
                </div>

                <div class="form-group">
                    <label for="edit_panchayath">Panchayath:</label>
                    <input type="text" id="edit_panchayath" name="panchayath" placeholder="e.g., KUMBLA GRAMA PANCHAYATH">
                </div>

                <div class="form-group">
                    <label for="edit_client_name">Client Name:</label>
                    <input type="text" id="edit_client_name" name="client_name" placeholder="e.g., JOHN DOE">
                </div>

                <div class="form-group">
                    <label for="edit_project_type">Project Type:</label>
                    <select id="edit_project_type" name="project_type">
                        <option value="HOUSE">House</option>
                        <option value="COMMERCIAL BUILDING">Commercial Building</option>
                        <option value="APARTMENT">Apartment</option>
                        <option value="OFFICE">Office</option>
                        <option value="WAREHOUSE">Warehouse</option>
                        <option value="OTHER">Other</option>
                    </select>
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn" onclick="closeModal('editProjectModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Project</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Upload DXF Modal -->
    <div id="uploadDxfModal" class="modal">
        <div class="modal-content">
            <h2>Upload DXF File</h2>
            <form id="uploadDxfForm">
                <input type="hidden" id="uploadProjectId" name="project_id">

                <div class="form-group">
                    <label for="upload_dxf_file">DXF File: *</label>
                    <div class="file-input-wrapper">
                        <input type="file" id="upload_dxf_file" name="dxf_file" class="file-input" accept=".dxf" required>
                        <label for="upload_dxf_file" class="file-input-label" id="uploadFileLabel">
                            <div style="font-size: 24px; margin-bottom: 8px;">📁</div>
                            <div>Click to select DXF file or drag and drop here</div>
                        </label>
                        <div class="file-selected" id="uploadFileSelected"></div>
                    </div>
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn" onclick="closeModal('uploadDxfModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="uploadDxfBtn" disabled>Upload & Generate</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Project form submission
        document.getElementById('projectForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const file = formData.get('dxf_file');

            if (!file || file.size === 0) {
                alert('Please select a DXF file.');
                return;
            }

            const projectData = {
                action: 'create',
                rs_no: formData.get('rs_no'),
                ward_no: formData.get('ward_no'),
                panchayath: formData.get('panchayath'),
                client_name: formData.get('client_name'),
                project_type: formData.get('project_type')
            };

            try {
                const createBtn = document.getElementById('createProjectBtn');
                createBtn.textContent = 'Creating...';
                createBtn.disabled = true;

                const response = await fetch('project_manager.php?api=projects', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(projectData)
                });

                const result = await response.json();

                if (result.success) {
                    createBtn.textContent = 'Uploading...';

                    const uploadFormData = new FormData();
                    uploadFormData.append('dxf_file', file);
                    uploadFormData.append('project_id', result.project.id);

                    const uploadResponse = await fetch('upload_handler.php', {
                        method: 'POST',
                        body: uploadFormData
                    });

                    const uploadResult = await uploadResponse.json();

                    if (uploadResult.success) {
                        window.location.href = `cpwd_estimate.php?project=${result.project.id}`;
                    } else {
                        alert('Upload failed: ' + uploadResult.error);
                        createBtn.textContent = 'Create Project';
                        createBtn.disabled = false;
                    }
                } else {
                    alert('Error creating project: ' + result.error);
                    createBtn.textContent = 'Create Project';
                    createBtn.disabled = false;
                }
            } catch (error) {
                alert('Error: ' + error.message);
                createBtn.textContent = 'Create Project';
                createBtn.disabled = false;
            }
        });

        // File handling
        const fileInput = document.getElementById('dxfFile');
        const fileSelected = document.getElementById('fileSelected');
        const createBtn = document.getElementById('createProjectBtn');

        fileInput.addEventListener('change', function(e) {
            handleFileSelect(e.target.files[0]);
        });

        function handleFileSelect(file) {
            if (!file) {
                fileSelected.style.display = 'none';
                createBtn.disabled = true;
                return;
            }

            if (!file.name.toLowerCase().endsWith('.dxf')) {
                alert('Please select a valid DXF file.');
                fileSelected.style.display = 'none';
                createBtn.disabled = true;
                return;
            }

            if (file.size > 50 * 1024 * 1024) {
                alert('File size must be less than 50MB.');
                fileSelected.style.display = 'none';
                createBtn.disabled = true;
                return;
            }

            fileSelected.innerHTML = `<strong>Selected:</strong> ${file.name}`;
            fileSelected.style.display = 'block';
            createBtn.disabled = false;
        }

        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function editProject(projectId, projectData) {
            document.getElementById('editProjectId').value = projectId;
            document.getElementById('edit_rs_no').value = projectData.rs_no || '';
            document.getElementById('edit_ward_no').value = projectData.ward_no || '';
            document.getElementById('edit_panchayath').value = projectData.panchayath || '';
            document.getElementById('edit_client_name').value = projectData.client_name || '';
            document.getElementById('edit_project_type').value = projectData.project_type || 'HOUSE';
            openModal('editProjectModal');
        }

        function uploadDxfForProject(projectId) {
            document.getElementById('uploadProjectId').value = projectId;
            openModal('uploadDxfModal');
        }

        function editRates(projectId) {
            window.location.href = `cpwd_estimate.php?project=${projectId}&edit_rates=1`;
        }

        async function deleteProject(projectId) {
            if (!confirm('Are you sure you want to delete this project?')) return;

            try {
                const response = await fetch('project_manager.php?api=projects', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'delete', id: projectId })
                });

                const result = await response.json();
                if (result.success) {
                    location.reload();
                } else {
                    alert('Error deleting project: ' + result.error);
                }
            } catch (error) {
                alert('Error deleting project: ' + error.message);
            }
        }

        function loadProjects() {
            location.reload();
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }
    </script>
</body>
</html>
