#!/usr/bin/env python3
"""
DXF File Processing Script for SmartEstimate
This script processes uploaded DXF files and extracts relevant information.
"""

import sys
import json
import os
import traceback
from datetime import datetime

def log_error(message):
    """Log error messages to a file"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    log_message = f"[{timestamp}] ERROR: {message}\n"
    
    try:
        with open('python_error.log', 'a') as f:
            f.write(log_message)
    except:
        pass  # Fail silently if logging fails

def validate_dxf_file(file_path):
    """Basic validation of DXF file format"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            first_line = f.readline().strip()
            if first_line != '0':
                return False, "Invalid DXF format: File should start with '0'"
            
            # Read a few more lines to check for SECTION
            for _ in range(10):
                line = f.readline().strip()
                if line == 'SECTION':
                    return True, "Valid DXF format detected"
            
            return False, "Invalid DXF format: SECTION not found in header"
    
    except UnicodeDecodeError:
        # Try with different encoding
        try:
            with open(file_path, 'r', encoding='latin-1') as f:
                first_line = f.readline().strip()
                return first_line == '0', "DXF format validation with latin-1 encoding"
        except:
            return False, "Unable to read file with any encoding"
    
    except Exception as e:
        return False, f"Error reading file: {str(e)}"

def extract_dxf_info(file_path):
    """Extract basic information from DXF file"""
    info = {
        'entities': {},
        'layers': set(),
        'blocks': set(),
        'file_size': 0,
        'line_count': 0,
        'layer_entities': {},  # Store entities by layer
        'wall_calculations': {}  # Store wall layer calculations
    }

    try:
        # Get file size
        info['file_size'] = os.path.getsize(file_path)

        current_section = None
        current_entity = None
        current_layer = None
        entity_count = 0

        # Store entities with their layer information
        entities_data = []

        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
            info['line_count'] = len(lines)

            i = 0
            while i < len(lines):
                line = lines[i].strip()

                # Track sections
                if line == 'SECTION':
                    if i + 1 < len(lines):
                        section_name = lines[i + 2].strip()
                        current_section = section_name

                # Track entities in ENTITIES section
                elif current_section == 'ENTITIES' and line == '0' and i + 1 < len(lines):
                    next_line = lines[i + 1].strip()
                    if next_line not in ['ENDSEC', 'EOF']:
                        current_entity = next_line
                        current_layer = None

                        # Count entities
                        if current_entity in info['entities']:
                            info['entities'][current_entity] += 1
                        else:
                            info['entities'][current_entity] = 1
                        entity_count += 1

                        # Extract entity data for specific types
                        if current_entity in ['LWPOLYLINE', 'POLYLINE', 'LINE', 'CIRCLE', 'ARC']:
                            entity_data = {
                                'type': current_entity,
                                'layer': None,
                                'coordinates': [],
                                'area': 0,
                                'length': 0
                            }
                            entities_data.append(entity_data)

                # Track layers
                elif line == '8' and i + 1 < len(lines):  # Layer code
                    layer_name = lines[i + 1].strip()
                    if layer_name and layer_name != '0':
                        info['layers'].add(layer_name)
                        current_layer = layer_name

                        # Update current entity's layer
                        if entities_data and entities_data[-1]['layer'] is None:
                            entities_data[-1]['layer'] = layer_name

                        # Initialize layer entity count
                        if layer_name not in info['layer_entities']:
                            info['layer_entities'][layer_name] = {}
                        if current_entity and current_entity not in info['layer_entities'][layer_name]:
                            info['layer_entities'][layer_name][current_entity] = 0
                        if current_entity:
                            info['layer_entities'][layer_name][current_entity] += 1

                # Track blocks
                elif current_section == 'BLOCKS' and line == '2' and i + 1 < len(lines):
                    block_name = lines[i + 1].strip()
                    if block_name and not block_name.startswith('*'):
                        info['blocks'].add(block_name)

                i += 1

        # Convert sets to lists for JSON serialization
        info['layers'] = list(info['layers'])
        info['blocks'] = list(info['blocks'])
        info['total_entities'] = entity_count

        # Calculate wall layer information
        info['wall_calculations'] = calculate_wall_layers(file_path, info['layers'])

        return info

    except Exception as e:
        log_error(f"Error extracting DXF info: {str(e)}")
        return None

def process_dxf_file(file_path):
    """Main function to process DXF file"""
    result = {
        'success': False,
        'message': '',
        'data': {}
    }
    
    try:
        # Check if file exists
        if not os.path.exists(file_path):
            result['message'] = f"File not found: {file_path}"
            return result
        
        # Validate DXF format
        is_valid, validation_message = validate_dxf_file(file_path)
        if not is_valid:
            result['message'] = f"Invalid DXF file: {validation_message}"
            return result
        
        # Extract information from DXF
        dxf_info = extract_dxf_info(file_path)
        if dxf_info is None:
            result['message'] = "Failed to extract information from DXF file"
            return result
        
        # Prepare result
        result['success'] = True
        result['message'] = "DXF file processed successfully"
        result['data'] = {
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'processed_at': datetime.now().isoformat(),
            'validation': validation_message,
            'info': dxf_info
        }
        
        # Add some basic analysis
        analysis = analyze_dxf_content(dxf_info)
        result['data']['analysis'] = analysis
        
        return result
    
    except Exception as e:
        log_error(f"Error processing DXF file {file_path}: {str(e)}\n{traceback.format_exc()}")
        result['message'] = f"Processing error: {str(e)}"
        return result

def calculate_wall_layers(file_path, layers):
    """Calculate areas and lengths for wall layers"""
    wall_calculations = {
        'WALLS_10CM': {'total_area': 0, 'total_length': 0, 'width': 0.10, 'rectangles': []},
        'WALLS_20CM': {'total_area': 0, 'total_length': 0, 'width': 0.20, 'rectangles': []},
        'WALLS_23CM': {'total_area': 0, 'total_length': 0, 'width': 0.23, 'rectangles': []}
    }

    ground_floor_boundary = None

    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()

            current_section = None
            current_entity = None
            current_layer = None
            entity_data = {}

            i = 0
            while i < len(lines):
                line = lines[i].strip()

                # Track sections
                if line == 'SECTION':
                    if i + 2 < len(lines):
                        section_name = lines[i + 2].strip()
                        current_section = section_name

                # Process entities in ENTITIES section
                elif current_section == 'ENTITIES':
                    if line == '0' and i + 1 < len(lines):
                        # Save previous entity if it was a wall layer
                        if current_entity and current_layer in wall_calculations and entity_data:
                            process_wall_entity(entity_data, wall_calculations[current_layer])
                        elif current_entity and current_layer == 'GROUND_FLOOR' and entity_data:
                            ground_floor_boundary = entity_data

                        # Start new entity
                        next_line = lines[i + 1].strip()
                        if next_line not in ['ENDSEC', 'EOF']:
                            current_entity = next_line
                            current_layer = None
                            entity_data = {'type': current_entity, 'coordinates': []}

                    elif line == '8' and i + 1 < len(lines):  # Layer code
                        current_layer = lines[i + 1].strip()
                        entity_data['layer'] = current_layer

                    elif line in ['10', '20'] and i + 1 < len(lines):  # X, Y coordinates
                        try:
                            coord_value = float(lines[i + 1].strip())
                            if line == '10':  # X coordinate
                                entity_data['coordinates'].append([coord_value, 0])
                            elif line == '20' and entity_data['coordinates']:  # Y coordinate
                                entity_data['coordinates'][-1][1] = coord_value
                        except ValueError:
                            pass

                    elif line in ['11', '21'] and i + 1 < len(lines):  # Second point for lines
                        try:
                            coord_value = float(lines[i + 1].strip())
                            if line == '11':  # X coordinate of second point
                                if len(entity_data['coordinates']) == 1:
                                    entity_data['coordinates'].append([coord_value, 0])
                            elif line == '21' and len(entity_data['coordinates']) >= 2:  # Y coordinate
                                entity_data['coordinates'][-1][1] = coord_value
                        except ValueError:
                            pass

                i += 1

            # Process last entity
            if current_entity and current_layer in wall_calculations and entity_data:
                process_wall_entity(entity_data, wall_calculations[current_layer])
            elif current_entity and current_layer == 'GROUND_FLOOR' and entity_data:
                ground_floor_boundary = entity_data

        # Calculate totals and lengths
        for layer_name, calc_data in wall_calculations.items():
            if calc_data['total_area'] > 0:
                calc_data['total_length'] = calc_data['total_area'] / calc_data['width']

        return wall_calculations

    except Exception as e:
        log_error(f"Error calculating wall layers: {str(e)}")
        return wall_calculations

def process_wall_entity(entity_data, layer_calc):
    """Process a single wall entity and calculate its area"""
    try:
        if entity_data['type'] in ['LWPOLYLINE', 'POLYLINE'] and len(entity_data['coordinates']) >= 3:
            # Calculate area of polygon using shoelace formula
            coords = entity_data['coordinates']
            area = 0
            n = len(coords)

            for i in range(n):
                j = (i + 1) % n
                area += coords[i][0] * coords[j][1]
                area -= coords[j][0] * coords[i][1]

            area = abs(area) / 2.0

            if area > 0:
                layer_calc['total_area'] += area
                layer_calc['rectangles'].append({
                    'area': area,
                    'coordinates': coords,
                    'length': area / layer_calc['width']
                })

        elif entity_data['type'] == 'LINE' and len(entity_data['coordinates']) == 2:
            # For lines, calculate length and assume width
            x1, y1 = entity_data['coordinates'][0]
            x2, y2 = entity_data['coordinates'][1]
            length = ((x2 - x1) ** 2 + (y2 - y1) ** 2) ** 0.5
            area = length * layer_calc['width']

            if area > 0:
                layer_calc['total_area'] += area
                layer_calc['rectangles'].append({
                    'area': area,
                    'coordinates': entity_data['coordinates'],
                    'length': length
                })

    except Exception as e:
        log_error(f"Error processing wall entity: {str(e)}")

def analyze_dxf_content(dxf_info):
    """Perform basic analysis of DXF content"""
    analysis = {
        'complexity': 'Unknown',
        'drawing_type': 'Unknown',
        'recommendations': []
    }
    
    try:
        total_entities = dxf_info.get('total_entities', 0)
        entity_types = len(dxf_info.get('entities', {}))
        layer_count = len(dxf_info.get('layers', []))
        
        # Determine complexity
        if total_entities < 100:
            analysis['complexity'] = 'Simple'
        elif total_entities < 1000:
            analysis['complexity'] = 'Medium'
        else:
            analysis['complexity'] = 'Complex'
        
        # Analyze entity types
        entities = dxf_info.get('entities', {})
        if 'LINE' in entities or 'POLYLINE' in entities:
            if 'CIRCLE' in entities or 'ARC' in entities:
                analysis['drawing_type'] = 'Mixed (Lines and Curves)'
            else:
                analysis['drawing_type'] = 'Linear Drawing'
        elif 'CIRCLE' in entities or 'ARC' in entities:
            analysis['drawing_type'] = 'Curved Drawing'
        elif 'TEXT' in entities or 'MTEXT' in entities:
            analysis['drawing_type'] = 'Text-heavy Drawing'
        
        # Generate recommendations
        if layer_count > 10:
            analysis['recommendations'].append('Consider organizing layers for better management')
        
        if total_entities > 5000:
            analysis['recommendations'].append('Large drawing - processing may take longer')
        
        if entity_types < 3:
            analysis['recommendations'].append('Simple drawing with few entity types')
        
        # Add entity summary
        analysis['entity_summary'] = {
            'total_entities': total_entities,
            'entity_types': entity_types,
            'layer_count': layer_count,
            'most_common_entities': sorted(entities.items(), key=lambda x: x[1], reverse=True)[:5]
        }
        
    except Exception as e:
        log_error(f"Error in DXF analysis: {str(e)}")
        analysis['error'] = str(e)
    
    return analysis

def main():
    """Main entry point"""
    if len(sys.argv) != 2:
        result = {
            'success': False,
            'message': 'Usage: python3 process_dxf.py <dxf_file_path>'
        }
        print(json.dumps(result))
        sys.exit(1)
    
    file_path = sys.argv[1]
    result = process_dxf_file(file_path)
    
    # Output result as JSON
    print(json.dumps(result, indent=2))

if __name__ == '__main__':
    main()
