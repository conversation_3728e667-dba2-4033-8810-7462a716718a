#!/usr/bin/env python3
"""
DXF File Processing Script for SmartEstimate
This script processes uploaded DXF files and extracts relevant information.
"""

import sys
import json
import os
import traceback
from datetime import datetime

def log_error(message):
    """Log error messages to a file"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    log_message = f"[{timestamp}] ERROR: {message}\n"
    
    try:
        with open('python_error.log', 'a') as f:
            f.write(log_message)
    except:
        pass  # Fail silently if logging fails

def validate_dxf_file(file_path):
    """Basic validation of DXF file format"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            first_line = f.readline().strip()
            if first_line != '0':
                return False, "Invalid DXF format: File should start with '0'"
            
            # Read a few more lines to check for SECTION
            for _ in range(10):
                line = f.readline().strip()
                if line == 'SECTION':
                    return True, "Valid DXF format detected"
            
            return False, "Invalid DXF format: SECTION not found in header"
    
    except UnicodeDecodeError:
        # Try with different encoding
        try:
            with open(file_path, 'r', encoding='latin-1') as f:
                first_line = f.readline().strip()
                return first_line == '0', "DXF format validation with latin-1 encoding"
        except:
            return False, "Unable to read file with any encoding"
    
    except Exception as e:
        return False, f"Error reading file: {str(e)}"

def extract_dxf_info(file_path):
    """Extract basic information from DXF file"""
    info = {
        'entities': {},
        'layers': set(),
        'blocks': set(),
        'file_size': 0,
        'line_count': 0
    }
    
    try:
        # Get file size
        info['file_size'] = os.path.getsize(file_path)
        
        current_section = None
        current_entity = None
        entity_count = 0
        
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
            info['line_count'] = len(lines)
            
            i = 0
            while i < len(lines):
                line = lines[i].strip()
                
                # Track sections
                if line == 'SECTION':
                    if i + 1 < len(lines):
                        section_name = lines[i + 2].strip()
                        current_section = section_name
                
                # Track entities
                elif current_section == 'ENTITIES' and line == '0' and i + 1 < len(lines):
                    next_line = lines[i + 1].strip()
                    if next_line not in ['ENDSEC', 'EOF']:
                        current_entity = next_line
                        if current_entity in info['entities']:
                            info['entities'][current_entity] += 1
                        else:
                            info['entities'][current_entity] = 1
                        entity_count += 1
                
                # Track layers
                elif line == '8' and i + 1 < len(lines):  # Layer code
                    layer_name = lines[i + 1].strip()
                    if layer_name and layer_name != '0':
                        info['layers'].add(layer_name)
                
                # Track blocks
                elif current_section == 'BLOCKS' and line == '2' and i + 1 < len(lines):
                    block_name = lines[i + 1].strip()
                    if block_name and not block_name.startswith('*'):
                        info['blocks'].add(block_name)
                
                i += 1
        
        # Convert sets to lists for JSON serialization
        info['layers'] = list(info['layers'])
        info['blocks'] = list(info['blocks'])
        info['total_entities'] = entity_count
        
        return info
    
    except Exception as e:
        log_error(f"Error extracting DXF info: {str(e)}")
        return None

def process_dxf_file(file_path):
    """Main function to process DXF file"""
    result = {
        'success': False,
        'message': '',
        'data': {}
    }
    
    try:
        # Check if file exists
        if not os.path.exists(file_path):
            result['message'] = f"File not found: {file_path}"
            return result
        
        # Validate DXF format
        is_valid, validation_message = validate_dxf_file(file_path)
        if not is_valid:
            result['message'] = f"Invalid DXF file: {validation_message}"
            return result
        
        # Extract information from DXF
        dxf_info = extract_dxf_info(file_path)
        if dxf_info is None:
            result['message'] = "Failed to extract information from DXF file"
            return result
        
        # Prepare result
        result['success'] = True
        result['message'] = "DXF file processed successfully"
        result['data'] = {
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'processed_at': datetime.now().isoformat(),
            'validation': validation_message,
            'info': dxf_info
        }
        
        # Add some basic analysis
        analysis = analyze_dxf_content(dxf_info)
        result['data']['analysis'] = analysis
        
        return result
    
    except Exception as e:
        log_error(f"Error processing DXF file {file_path}: {str(e)}\n{traceback.format_exc()}")
        result['message'] = f"Processing error: {str(e)}"
        return result

def analyze_dxf_content(dxf_info):
    """Perform basic analysis of DXF content"""
    analysis = {
        'complexity': 'Unknown',
        'drawing_type': 'Unknown',
        'recommendations': []
    }
    
    try:
        total_entities = dxf_info.get('total_entities', 0)
        entity_types = len(dxf_info.get('entities', {}))
        layer_count = len(dxf_info.get('layers', []))
        
        # Determine complexity
        if total_entities < 100:
            analysis['complexity'] = 'Simple'
        elif total_entities < 1000:
            analysis['complexity'] = 'Medium'
        else:
            analysis['complexity'] = 'Complex'
        
        # Analyze entity types
        entities = dxf_info.get('entities', {})
        if 'LINE' in entities or 'POLYLINE' in entities:
            if 'CIRCLE' in entities or 'ARC' in entities:
                analysis['drawing_type'] = 'Mixed (Lines and Curves)'
            else:
                analysis['drawing_type'] = 'Linear Drawing'
        elif 'CIRCLE' in entities or 'ARC' in entities:
            analysis['drawing_type'] = 'Curved Drawing'
        elif 'TEXT' in entities or 'MTEXT' in entities:
            analysis['drawing_type'] = 'Text-heavy Drawing'
        
        # Generate recommendations
        if layer_count > 10:
            analysis['recommendations'].append('Consider organizing layers for better management')
        
        if total_entities > 5000:
            analysis['recommendations'].append('Large drawing - processing may take longer')
        
        if entity_types < 3:
            analysis['recommendations'].append('Simple drawing with few entity types')
        
        # Add entity summary
        analysis['entity_summary'] = {
            'total_entities': total_entities,
            'entity_types': entity_types,
            'layer_count': layer_count,
            'most_common_entities': sorted(entities.items(), key=lambda x: x[1], reverse=True)[:5]
        }
        
    except Exception as e:
        log_error(f"Error in DXF analysis: {str(e)}")
        analysis['error'] = str(e)
    
    return analysis

def main():
    """Main entry point"""
    if len(sys.argv) != 2:
        result = {
            'success': False,
            'message': 'Usage: python3 process_dxf.py <dxf_file_path>'
        }
        print(json.dumps(result))
        sys.exit(1)
    
    file_path = sys.argv[1]
    result = process_dxf_file(file_path)
    
    # Output result as JSON
    print(json.dumps(result, indent=2))

if __name__ == '__main__':
    main()
