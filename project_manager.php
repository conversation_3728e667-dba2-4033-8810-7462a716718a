<?php
/**
 * Project Management System for SmartEstimate
 */

class ProjectManager {
    private $projectsFile = 'data/projects.json';
    private $dataDir = 'data';
    
    public function __construct() {
        $this->ensureDataDirectory();
    }
    
    private function ensureDataDirectory() {
        if (!is_dir($this->dataDir)) {
            mkdir($this->dataDir, 0755, true);
        }
        
        if (!file_exists($this->projectsFile)) {
            file_put_contents($this->projectsFile, json_encode([]));
        }
    }
    
    public function createProject($projectData) {
        $projects = $this->getAllProjects();
        
        $project = [
            'id' => uniqid('proj_'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'rs_no' => $projectData['rs_no'] ?? '',
            'ward_no' => $projectData['ward_no'] ?? '',
            'panchayath' => $projectData['panchayath'] ?? '',
            'client_name' => $projectData['client_name'] ?? '',
            'project_type' => $projectData['project_type'] ?? 'HOUSE',
            'dxf_file' => null,
            'estimate_data' => null,
            'status' => 'created'
        ];
        
        $projects[] = $project;
        file_put_contents($this->projectsFile, json_encode($projects, JSON_PRETTY_PRINT));
        
        return $project;
    }
    
    public function updateProject($projectId, $updateData) {
        $projects = $this->getAllProjects();
        
        foreach ($projects as &$project) {
            if ($project['id'] === $projectId) {
                foreach ($updateData as $key => $value) {
                    $project[$key] = $value;
                }
                $project['updated_at'] = date('Y-m-d H:i:s');
                break;
            }
        }
        
        file_put_contents($this->projectsFile, json_encode($projects, JSON_PRETTY_PRINT));
        return $this->getProject($projectId);
    }
    
    public function getProject($projectId) {
        $projects = $this->getAllProjects();
        
        foreach ($projects as $project) {
            if ($project['id'] === $projectId) {
                return $project;
            }
        }
        
        return null;
    }
    
    public function getAllProjects() {
        if (!file_exists($this->projectsFile)) {
            return [];
        }
        
        $content = file_get_contents($this->projectsFile);
        $projects = json_decode($content, true);
        
        if (!is_array($projects)) {
            return [];
        }
        
        // Sort by created_at descending (newest first)
        usort($projects, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        
        return $projects;
    }
    
    public function deleteProject($projectId) {
        $projects = $this->getAllProjects();
        $projects = array_filter($projects, function($project) use ($projectId) {
            return $project['id'] !== $projectId;
        });
        
        file_put_contents($this->projectsFile, json_encode(array_values($projects), JSON_PRETTY_PRINT));
        return true;
    }
    
    public function generateProjectTitle($project) {
        $title = "DETAILED CUM ABSTRACTED ESTIMATE FOR THE PROPOSED CONSTRUCTION OF A " .
                 strtoupper($project['project_type'] ?? 'HOUSE');

        if (!empty($project['rs_no']) && trim($project['rs_no']) !== '') {
            $title .= " IN R.S.No: " . strtoupper(trim($project['rs_no']));
        }

        if (!empty($project['ward_no']) && trim($project['ward_no']) !== '') {
            $title .= " WARD No: " . strtoupper(trim($project['ward_no']));
        }

        if (!empty($project['panchayath']) && trim($project['panchayath']) !== '') {
            $title .= " OF " . strtoupper(trim($project['panchayath']));
        }

        if (!empty($project['client_name']) && trim($project['client_name']) !== '') {
            $title .= " FOR " . strtoupper(trim($project['client_name']));
        }

        return $title;
    }
    
    public function getProjectStats() {
        $projects = $this->getAllProjects();
        
        return [
            'total' => count($projects),
            'with_estimates' => count(array_filter($projects, function($p) { 
                return !empty($p['estimate_data']); 
            })),
            'recent' => count(array_filter($projects, function($p) { 
                return strtotime($p['created_at']) > strtotime('-7 days'); 
            }))
        ];
    }
}

// Helper functions for API endpoints
function handleProjectAPI() {
    header('Content-Type: application/json');
    
    $projectManager = new ProjectManager();
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'POST':
            $data = json_decode(file_get_contents('php://input'), true);
            if (isset($data['action'])) {
                switch ($data['action']) {
                    case 'create':
                        $project = $projectManager->createProject($data);
                        echo json_encode(['success' => true, 'project' => $project]);
                        break;
                    case 'update':
                        $project = $projectManager->updateProject($data['id'], $data);
                        echo json_encode(['success' => true, 'project' => $project]);
                        break;
                    case 'delete':
                        $projectManager->deleteProject($data['id']);
                        echo json_encode(['success' => true]);
                        break;
                    default:
                        echo json_encode(['success' => false, 'error' => 'Invalid action']);
                }
            } else {
                echo json_encode(['success' => false, 'error' => 'No action specified']);
            }
            break;
            
        case 'GET':
            if (isset($_GET['id'])) {
                $project = $projectManager->getProject($_GET['id']);
                echo json_encode(['success' => true, 'project' => $project]);
            } else {
                $projects = $projectManager->getAllProjects();
                $stats = $projectManager->getProjectStats();
                echo json_encode(['success' => true, 'projects' => $projects, 'stats' => $stats]);
            }
            break;
            
        default:
            echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    }
    exit;
}

// Check if this is an API call
if (isset($_GET['api']) && $_GET['api'] === 'projects') {
    handleProjectAPI();
}
?>
