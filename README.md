# SmartEstimate DXF File Upload System

A PHP-based web application for uploading and processing DXF files with a Python backend.

## Features

- **Secure File Upload**: Only accepts .dxf files with size validation (max 50MB)
- **Progress Indication**: Real-time upload progress with visual feedback
- **File Validation**: Validates DXF file format and structure
- **Python Processing**: Extracts detailed information from DXF files
- **Security**: Protected uploads directory and input sanitization

## Requirements

- PHP 7.4 or higher
- Python 3.6 or higher
- Web server (Apache/Nginx) or PHP built-in server
- Write permissions for uploads directory

## Quick Start

### Option 1: Using PHP Built-in Server (Recommended for Development)

1. Open terminal in the project directory
2. Start the PHP server:
   ```bash
   php -S localhost:8000
   ```
3. Open your browser and go to: `http://localhost:8000`

### Option 2: Using XAMPP/WAMP/MAMP

1. Copy the project folder to your web server's document root (htdocs/www)
2. Start Apache in your control panel
3. Access via: `http://localhost/SmartEstimate`

### Option 3: Using Apache/Nginx

1. Configure a virtual host pointing to the project directory
2. Ensure PHP is enabled
3. Set proper permissions for the uploads directory

## File Structure

```
SmartEstimate/
├── index.php              # Main upload form
├── upload_handler.php     # PHP backend for file processing
├── process_dxf.py        # Python script for DXF analysis
├── uploads/              # Secure upload directory
│   ├── .htaccess        # Prevents direct access
│   └── index.php        # Additional security
├── error.log            # PHP error log (created automatically)
├── upload.log           # Upload activity log (created automatically)
├── python_error.log     # Python error log (created automatically)
└── README.md           # This file
```

## Usage

1. **Upload a DXF File**:
   - Click "Choose File" or drag and drop a .dxf file
   - Only DXF files up to 50MB are accepted
   - Click "Process DXF File" to upload

2. **Processing**:
   - The file is validated and saved securely
   - Python script analyzes the DXF content
   - Results include entity counts, layers, and complexity analysis

3. **Results**:
   - Success/error messages are displayed
   - Detailed analysis is returned in JSON format

## Security Features

- File type validation (extension and MIME type)
- File size limits
- Secure filename generation
- Protected uploads directory
- Input sanitization
- Error logging

## DXF Analysis Features

The Python script extracts:
- Entity types and counts (lines, circles, arcs, etc.)
- Layer information
- Block definitions
- Drawing complexity assessment
- File structure validation
- Recommendations for optimization

## Troubleshooting

### Common Issues

1. **"Permission denied" errors**:
   ```bash
   chmod 755 uploads/
   chmod +x process_dxf.py
   ```

2. **Python script not found**:
   - Ensure Python 3 is installed: `python3 --version`
   - Check the path in upload_handler.php

3. **Upload fails**:
   - Check PHP upload limits in php.ini:
     ```ini
     upload_max_filesize = 50M
     post_max_size = 50M
     max_execution_time = 300
     ```

4. **File not processing**:
   - Check error.log and python_error.log for details
   - Ensure the DXF file is valid

### Testing

Test with the included sample DXF file:
- `reference files/1097. Lalitha-Estimate.dxf`

## Development

To extend functionality:

1. **Add new DXF analysis features** in `process_dxf.py`
2. **Modify upload validation** in `upload_handler.php`
3. **Enhance UI** in `index.php`

## Logs

- `error.log`: PHP errors and warnings
- `upload.log`: File upload activity
- `python_error.log`: Python script errors

## License

This project is for educational and development purposes.
