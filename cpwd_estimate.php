<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CPWD Rate Estimate - SmartEstimate</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f8fafc;
            color: #1a202c;
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
        }

        .header {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            color: #0f172a;
            margin-bottom: 8px;
        }

        .header p {
            color: #64748b;
            font-size: 14px;
        }

        .nav-tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 24px;
        }

        .nav-tab {
            padding: 8px 16px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            text-decoration: none;
            color: #64748b;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }

        .nav-tab.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .nav-tab:hover:not(.active) {
            background: #f1f5f9;
            color: #1e293b;
        }

        .estimate-card {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .estimate-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }

        .estimate-table th {
            background: #f8fafc;
            padding: 12px 8px;
            text-align: center;
            font-weight: 600;
            color: #374151;
            border: 1px solid #e2e8f0;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .estimate-table td {
            padding: 12px 8px;
            border: 1px solid #e2e8f0;
            vertical-align: middle;
            text-align: center;
        }

        .estimate-table td.description {
            text-align: left;
            max-width: 400px;
        }

        .estimate-table td.amount {
            text-align: right;
        }

        .estimate-table tr:hover {
            background: #f8fafc;
        }

        .item-description {
            font-weight: 500;
            color: #1e293b;
            max-width: 400px;
        }

        .sub-item {
            padding-left: 40px;
            font-size: 12px;
            color: #64748b;
        }

        .editable {
            background: transparent;
            border: 1px solid transparent;
            padding: 4px 6px;
            border-radius: 4px;
            font-size: 13px;
            width: 60px;
            text-align: center;
            transition: all 0.2s;
        }

        .editable:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
        }

        .editable:focus {
            outline: none;
            background: white;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .qty-cell {
            font-weight: 600;
            color: #1e293b;
        }

        .amount-cell {
            font-weight: 600;
            color: #059669;
            text-align: right;
        }

        .total-row {
            background: #f8fafc;
            font-weight: 600;
        }

        .total-row td {
            border-top: 2px solid #e2e8f0;
            padding: 16px 8px;
        }

        .rate-section {
            background: #fef3c7;
            padding: 12px;
            margin: 8px 0;
            border-radius: 6px;
            font-size: 12px;
            color: #92400e;
        }

        .controls {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            color: #374151;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn:hover {
            background: #f9fafb;
            border-color: #d1d5db;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .input-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .input-group label {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            min-width: 80px;
        }

        .input-group input {
            padding: 6px 10px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            width: 100px;
        }

        .formula {
            font-family: 'Courier New', monospace;
            background: #f1f5f9;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            color: #475569;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #64748b;
        }

        .error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 24px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><?php echo htmlspecialchars($projectTitle ?? 'DETAILED CUM ABSTRACTED ESTIMATE'); ?></h1>
            <p>Central Public Works Department standard rate estimation</p>
        </div>

        <div class="nav-tabs">
            <a href="index.php" class="nav-tab">← Back to Projects</a>
            <a href="cpwd_estimate.php<?php echo isset($_GET['project']) ? '?project=' . $_GET['project'] : (isset($_GET['data']) ? '?data=' . $_GET['data'] : ''); ?>" class="nav-tab active">CPWD Rate Estimate</a>
            <a href="#" class="nav-tab" onclick="alert('Material Estimate feature coming soon!')">Material Estimate</a>
        </div>

        <div class="controls">
            <button class="btn btn-primary" onclick="calculateAll()">Calculate All</button>
            <button class="btn" onclick="downloadExcel()">Download Excel</button>
            <button class="btn" onclick="window.print()">Print</button>
        </div>

        <?php
        require_once 'project_manager.php';
        $projectManager = new ProjectManager();

        $project = null;
        $resultData = null;
        $data = null;
        $wallCalculations = [];

        // Check if we have project ID or data parameter
        if (isset($_GET['project'])) {
            // Load from project
            $project = $projectManager->getProject($_GET['project']);
            if ($project && !empty($project['estimate_data'])) {
                $resultData = $project['estimate_data'];
                if (isset($resultData['data']['info']['wall_calculations'])) {
                    $data = $resultData['data'];
                    $wallCalculations = $data['info']['wall_calculations'];
                }
            }
        } elseif (isset($_GET['data'])) {
            // Load from URL data (legacy)
            $resultData = json_decode(base64_decode($_GET['data']), true);
            if (isset($resultData['data']['processing_result']['data'])) {
                $data = $resultData['data']['processing_result']['data'];
            } else {
                $data = $resultData['data'];
            }
            $wallCalculations = $data['info']['wall_calculations'] ?? [];
        }

        if (!$data || empty($wallCalculations)) {
            echo '<div class="error">';
            echo '<h3>No Estimate Data Available</h3>';
            echo '<p>Please upload a DXF file first to generate an estimate.</p>';
            echo '<a href="index.php" class="btn btn-primary">Add New Project</a>';
            echo '</div>';
            exit;
        }

        // Generate project title
        $projectTitle = "DETAILED CUM ABSTRACTED ESTIMATE FOR THE PROPOSED CONSTRUCTION OF A HOUSE";
        if ($project) {
            $projectTitle = $projectManager->generateProjectTitle($project);
        }

        // Ensure projectTitle is never null
        if (empty($projectTitle)) {
            $projectTitle = "DETAILED CUM ABSTRACTED ESTIMATE FOR THE PROPOSED CONSTRUCTION OF A HOUSE";
        }
        ?>



        <div class="estimate-card">
            <table class="estimate-table">
                <thead>
                    <tr>
                        <th style="width: 60px;">Sl.No.</th>
                        <th style="width: 400px;">DESCRIPTION</th>
                        <th style="width: 60px;">NO</th>
                        <th style="width: 80px;">L</th>
                        <th style="width: 80px;">B</th>
                        <th style="width: 80px;">D</th>
                        <th style="width: 100px;">QTY</th>
                        <th style="width: 120px;">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $totalQty = 0;
                    $rate = 145.20; // Rs per M3
                    $rowIndex = 0;
                    $totalRows = 0;

                    // Count total rows needed
                    foreach ($wallCalculations as $wallType => $calc) {
                        if ($calc['total_length'] > 0) {
                            $totalRows += count($calc['rectangles']);
                        }
                    }
                    $totalRows += 2; // For total and rate rows
                    ?>

                    <tr>
                        <td rowspan="<?php echo $totalRows; ?>" style="vertical-align: top; font-weight: 600; text-align: center;">1.</td>
                        <td class="description" style="text-align: left;">
                            Excavation for foundation including labour charges, etc. complete.
                            <div class="item-controls" style="margin-top: 10px; display: flex; gap: 15px; align-items: center;">
                                <div class="input-group" style="gap: 5px;">
                                    <label style="font-size: 12px; min-width: auto;">Default B:</label>
                                    <input type="number" id="defaultB_item1" value="0.7" step="0.1" min="0" style="width: 60px; padding: 4px 6px; font-size: 12px;">
                                    <span style="font-size: 12px;">m</span>
                                </div>
                                <div class="input-group" style="gap: 5px;">
                                    <label style="font-size: 12px; min-width: auto;">Default D:</label>
                                    <input type="number" id="defaultD_item1" value="1.2" step="0.1" min="0" style="width: 60px; padding: 4px 6px; font-size: 12px;">
                                    <span style="font-size: 12px;">m</span>
                                </div>
                                <div class="input-group" style="gap: 5px;">
                                    <label style="font-size: 12px; min-width: auto;">Rate (Rs/M³):</label>
                                    <input type="number" id="rateInput_item1" value="145.20" step="0.01" min="0" onchange="updateRate(1)" style="width: 80px; padding: 4px 6px; font-size: 12px;">
                                </div>
                                <button class="btn" onclick="applyDefaults(1)" style="padding: 4px 8px; font-size: 12px;">Apply</button>
                            </div>
                        </td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>

                    <?php
                    $firstRow = true;
                    foreach ($wallCalculations as $wallType => $calc):
                        if ($calc['total_length'] > 0):
                            foreach ($calc['rectangles'] as $index => $rectangle):
                                $rowIndex++;
                    ?>
                    <tr class="wall-row" data-wall-type="<?php echo $wallType; ?>" data-length="<?php echo $rectangle['length']; ?>" data-rectangle-index="<?php echo $index; ?>">
                        <td class="description" style="padding-left: 40px; font-size: 12px; text-align: left;">
                            <?php echo str_replace('_', ' ', $wallType); ?>
                            <?php if (count($calc['rectangles']) > 1): ?>
                                - Segment <?php echo $index + 1; ?>
                            <?php endif; ?>
                        </td>
                        <td>1</td>
                        <td class="length-value"><?php echo number_format($rectangle['length'], 2); ?></td>
                        <td><input type="number" class="editable b-value" value="0.7" step="0.1" min="0" onchange="calculateRow(this)"></td>
                        <td><input type="number" class="editable d-value" value="1.2" step="0.1" min="0" onchange="calculateRow(this)"></td>
                        <td>
                            <span class="qty-value">0.00</span>
                        </td>
                        <td></td>
                    </tr>
                    <?php
                            endforeach;
                        endif;
                    endforeach;
                    ?>

                    <tr class="total-row">
                        <td colspan="6" style="text-align: right; font-weight: 600; border-right: 1px solid #e2e8f0;">TOTAL</td>
                        <td style="text-align: center; font-weight: 600; border-right: 1px solid #e2e8f0;">
                            <span id="totalQty">0.00</span> M³
                        </td>
                        <td style="text-align: right; font-weight: 600;">
                            Rs. <span id="totalAmount">0.00</span>
                        </td>
                    </tr>

                    <tr>
                        <td colspan="7" class="rate-section">
                            Say <span id="totalQtyText">0.00</span> M³ @ Rs.<?php echo number_format($rate, 2); ?> per M³
                        </td>
                        <td class="rate-section" style="text-align: right; font-weight: 600;">
                            Rs. <span id="totalAmountRate">0.00</span>
                        </td>
                    </tr>

                    <!-- Item 2: Laterite Masonry -->
                    <?php
                    // Calculate total rows for Item 2 (Below GL + Basement + 2 sub-headers + 2 totals + 2 rate rows)
                    $item2WallCount = 0;
                    foreach ($wallCalculations as $wallType => $calc) {
                        if ($calc['total_length'] > 0) {
                            $item2WallCount += count($calc['rectangles']);
                        }
                    }
                    $item2TotalRows = ($item2WallCount * 2) + 6; // 2 sections * walls + 6 extra rows
                    ?>
                    <tr class="item-header">
                        <td rowspan="<?php echo $item2TotalRows; ?>" style="vertical-align: top; font-weight: 600; text-align: center;">2.</td>
                        <td class="description" style="font-weight: 600; text-align: left;">
                            Laterite masonary in cement mortar 1:6 for foundation including cost of materials, conveyance and labour charges, etc. complete.
                            <div class="item-controls" style="margin-top: 10px; display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                                <div style="display: flex; gap: 10px; align-items: center;">
                                    <span style="font-size: 12px; font-weight: 500;">Below GL:</span>
                                    <div class="input-group" style="gap: 5px;">
                                        <label style="font-size: 12px; min-width: auto;">B:</label>
                                        <input type="number" id="defaultB_item2_belowgl" value="0.53" step="0.01" min="0" style="width: 60px; padding: 4px 6px; font-size: 12px;">
                                        <span style="font-size: 12px;">m</span>
                                    </div>
                                    <div class="input-group" style="gap: 5px;">
                                        <label style="font-size: 12px; min-width: auto;">D:</label>
                                        <input type="number" id="defaultD_item2_belowgl" value="1.2" step="0.1" min="0" style="width: 60px; padding: 4px 6px; font-size: 12px;">
                                        <span style="font-size: 12px;">m</span>
                                    </div>
                                </div>
                                <div style="display: flex; gap: 10px; align-items: center;">
                                    <span style="font-size: 12px; font-weight: 500;">Basement:</span>
                                    <div class="input-group" style="gap: 5px;">
                                        <label style="font-size: 12px; min-width: auto;">B:</label>
                                        <input type="number" id="defaultB_item2_basement" value="0.3" step="0.01" min="0" style="width: 60px; padding: 4px 6px; font-size: 12px;">
                                        <span style="font-size: 12px;">m</span>
                                    </div>
                                    <div class="input-group" style="gap: 5px;">
                                        <label style="font-size: 12px; min-width: auto;">D:</label>
                                        <input type="number" id="defaultD_item2_basement" value="0.6" step="0.1" min="0" style="width: 60px; padding: 4px 6px; font-size: 12px;">
                                        <span style="font-size: 12px;">m</span>
                                    </div>
                                </div>
                                <div class="input-group" style="gap: 5px;">
                                    <label style="font-size: 12px; min-width: auto;">Rate (Rs/M³):</label>
                                    <input type="number" id="rateInput_item2" value="4190.00" step="0.01" min="0" onchange="updateRate(2)" style="width: 80px; padding: 4px 6px; font-size: 12px;">
                                </div>
                                <button class="btn" onclick="applyItem2Defaults()" style="padding: 4px 8px; font-size: 12px;">Apply</button>
                            </div>
                        </td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>

                    <!-- BELOW GL Section -->
                    <tr class="sub-header">
                        <td colspan="7" style="font-style: italic; color: #666; padding-left: 20px; text-align: left;">
                            BELOW GL
                        </td>
                    </tr>

                    <?php
                    $item2BelowGLQty = 0;
                    foreach ($wallCalculations as $wallType => $calc):
                        if ($calc['total_length'] > 0):
                            foreach ($calc['rectangles'] as $index => $rectangle):
                                $length = $rectangle['length'];
                                $width = 0.53; // Below GL width
                                $depth = 1.2; // Same as Item 1 default
                                $qty = $length * $width * $depth;
                                $item2BelowGLQty += $qty;
                                $wallName = $wallType . ' - Segment ' . ($index + 1);
                    ?>
                    <tr class="wall-row" data-length="<?php echo $length; ?>" data-item="2" data-section="below_gl">
                        <td style="padding-left: 40px; font-size: 12px; text-align: left;"><?php echo $wallName; ?></td>
                        <td>1</td>
                        <td><?php echo number_format($length, 2); ?></td>
                        <td><input type="number" class="editable b-value" value="0.53" step="0.01" min="0" onchange="calculateItem2Row(this)"></td>
                        <td><input type="number" class="editable d-value" value="1.2" step="0.1" min="0" onchange="calculateItem2Row(this)"></td>
                        <td>
                            <span class="qty-value"><?php echo number_format($qty, 2); ?></span>
                        </td>
                        <td></td>
                    </tr>
                    <?php
                            endforeach;
                        endif;
                    endforeach;
                    ?>

                    <!-- BASEMENT Section -->
                    <tr class="sub-header">
                        <td colspan="7" style="font-style: italic; color: #666; padding-left: 20px; text-align: left;">
                            BASEMENT
                        </td>
                    </tr>

                    <?php
                    $item2BasementQty = 0;
                    foreach ($wallCalculations as $wallType => $calc):
                        if ($calc['total_length'] > 0):
                            foreach ($calc['rectangles'] as $index => $rectangle):
                                $length = $rectangle['length'];
                                $width = 0.3; // Basement width
                                $depth = 0.6; // Basement depth
                                $qty = $length * $width * $depth;
                                $item2BasementQty += $qty;
                                $wallName = $wallType . ' - Segment ' . ($index + 1);
                    ?>
                    <tr class="wall-row" data-length="<?php echo $length; ?>" data-item="2" data-section="basement">
                        <td style="padding-left: 40px; font-size: 12px; text-align: left;"><?php echo $wallName; ?></td>
                        <td>1</td>
                        <td><?php echo number_format($length, 2); ?></td>
                        <td><input type="number" class="editable b-value" value="0.3" step="0.01" min="0" onchange="calculateItem2Row(this)"></td>
                        <td><input type="number" class="editable d-value" value="0.6" step="0.1" min="0" onchange="calculateItem2Row(this)"></td>
                        <td>
                            <span class="qty-value"><?php echo number_format($qty, 2); ?></span>
                        </td>
                        <td></td>
                    </tr>
                    <?php
                            endforeach;
                        endif;
                    endforeach;
                    ?>

                    <!-- Item 2 Total -->
                    <?php
                    $item2TotalQty = $item2BelowGLQty + $item2BasementQty;
                    $item2Rate = 4190.00;
                    $item2Amount = $item2TotalQty * $item2Rate;
                    ?>
                    <tr class="total-row">
                        <td colspan="6" style="text-align: right; font-weight: 600; border-right: 1px solid #e2e8f0;">TOTAL</td>
                        <td style="text-align: center; font-weight: 600; border-right: 1px solid #e2e8f0;">
                            <span id="item2TotalQty"><?php echo number_format($item2TotalQty, 2); ?></span> M³
                        </td>
                        <td style="text-align: right; font-weight: 600;">
                            Rs. <span id="item2TotalAmount"><?php echo number_format($item2Amount, 2); ?></span>
                        </td>
                    </tr>

                    <tr>
                        <td colspan="7" class="rate-section">
                            Say <span id="item2TotalQtyText"><?php echo number_format($item2TotalQty, 2); ?></span> M³ @ Rs.<?php echo number_format($item2Rate, 2); ?> per M³
                        </td>
                        <td class="rate-section" style="text-align: right; font-weight: 600;">
                            Rs. <span id="item2TotalAmountRate"><?php echo number_format($item2Amount, 2); ?></span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>


    </div>

    <script>
        let rates = {
            1: <?php echo $rate; ?>,
            2: <?php echo $item2Rate; ?>
        };

        function updateRate(itemNumber) {
            if (itemNumber === 1) {
                rates[itemNumber] = parseFloat(document.getElementById(`rateInput_item${itemNumber}`).value) || 145.20;
                calculateTotal();
            } else if (itemNumber === 2) {
                rates[itemNumber] = parseFloat(document.getElementById(`rateInput_item${itemNumber}`).value) || 4190.00;
                calculateItem2Total();
            }
        }

        function calculateRow(input) {
            const row = input.closest('.wall-row');
            const length = parseFloat(row.dataset.length);
            const b = parseFloat(row.querySelector('.b-value').value) || 0;
            const d = parseFloat(row.querySelector('.d-value').value) || 0;

            const qty = 1 * length * b * d;
            row.querySelector('.qty-value').textContent = qty.toFixed(2);

            calculateTotal();
        }

        function calculateItem2Row(input) {
            const row = input.closest('.wall-row');
            const length = parseFloat(row.dataset.length);
            const b = parseFloat(row.querySelector('.b-value').value) || 0;
            const d = parseFloat(row.querySelector('.d-value').value) || 0;

            const qty = 1 * length * b * d;
            row.querySelector('.qty-value').textContent = qty.toFixed(2);

            calculateItem2Total();
        }

        function calculateAll() {
            document.querySelectorAll('.wall-row').forEach(row => {
                const input = row.querySelector('.b-value');
                calculateRow(input);
            });
        }

        function calculateTotal() {
            let totalQty = 0;

            // Only calculate for Item 1 rows (those without data-item attribute or data-item="1")
            document.querySelectorAll('.wall-row:not([data-item="2"])').forEach(row => {
                const qty = parseFloat(row.querySelector('.qty-value').textContent) || 0;
                totalQty += qty;
            });

            const totalAmount = totalQty * rates[1];

            document.getElementById('totalQty').textContent = totalQty.toFixed(2);
            document.getElementById('totalQtyText').textContent = totalQty.toFixed(2);
            document.getElementById('totalAmount').textContent = totalAmount.toLocaleString('en-IN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
            document.getElementById('totalAmountRate').textContent = totalAmount.toLocaleString('en-IN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }

        function calculateItem2Total() {
            let belowGLQty = 0;
            let basementQty = 0;

            // Calculate Below GL quantity
            document.querySelectorAll('.wall-row[data-item="2"][data-section="below_gl"]').forEach(row => {
                const qty = parseFloat(row.querySelector('.qty-value').textContent) || 0;
                belowGLQty += qty;
            });

            // Calculate Basement quantity
            document.querySelectorAll('.wall-row[data-item="2"][data-section="basement"]').forEach(row => {
                const qty = parseFloat(row.querySelector('.qty-value').textContent) || 0;
                basementQty += qty;
            });

            const totalQty = belowGLQty + basementQty;
            const totalAmount = totalQty * rates[2];

            document.getElementById('item2TotalQty').textContent = totalQty.toFixed(2);
            document.getElementById('item2TotalQtyText').textContent = totalQty.toFixed(2);
            document.getElementById('item2TotalAmount').textContent = totalAmount.toLocaleString('en-IN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
            document.getElementById('item2TotalAmountRate').textContent = totalAmount.toLocaleString('en-IN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }

        function applyDefaults(itemNumber) {
            const defaultB = parseFloat(document.getElementById(`defaultB_item${itemNumber}`).value) || 0.7;
            const defaultD = parseFloat(document.getElementById(`defaultD_item${itemNumber}`).value) || 1.2;

            // Apply to Item 1 rows only
            document.querySelectorAll('.wall-row:not([data-item="2"]) .b-value').forEach(input => {
                input.value = defaultB;
            });

            document.querySelectorAll('.wall-row:not([data-item="2"]) .d-value').forEach(input => {
                input.value = defaultD;
            });

            calculateAll();
        }

        function applyItem2Defaults() {
            const belowGLB = parseFloat(document.getElementById('defaultB_item2_belowgl').value) || 0.53;
            const belowGLD = parseFloat(document.getElementById('defaultD_item2_belowgl').value) || 1.2;
            const basementB = parseFloat(document.getElementById('defaultB_item2_basement').value) || 0.3;
            const basementD = parseFloat(document.getElementById('defaultD_item2_basement').value) || 0.6;

            // Apply to Below GL rows
            document.querySelectorAll('.wall-row[data-item="2"][data-section="below_gl"] .b-value').forEach(input => {
                input.value = belowGLB;
            });
            document.querySelectorAll('.wall-row[data-item="2"][data-section="below_gl"] .d-value').forEach(input => {
                input.value = belowGLD;
            });

            // Apply to Basement rows
            document.querySelectorAll('.wall-row[data-item="2"][data-section="basement"] .b-value').forEach(input => {
                input.value = basementB;
            });
            document.querySelectorAll('.wall-row[data-item="2"][data-section="basement"] .d-value').forEach(input => {
                input.value = basementD;
            });

            calculateItem2Total();
        }

        function exportPDF() {
            alert('PDF export functionality will be implemented in the next phase.');
        }

        function downloadExcel() {
            // Create Excel data
            const data = [];

            // Header
            data.push(['DETAILED CUM ABSTRACTED ESTIMATE']);
            data.push(['Central Public Works Department']);
            data.push([]);
            data.push(['Sl.No.', 'DESCRIPTION', 'NO', 'L', 'B', 'D', 'QTY', 'AMOUNT']);

            // Item 1 header
            data.push(['1.', 'Excavation for foundation including labour charges, etc. complete.', '', '', '', '', '', '']);

            // Item 1 Wall data
            document.querySelectorAll('.wall-row:not([data-item="2"])').forEach(row => {
                const description = row.querySelector('.description').textContent.trim();
                const no = '1';
                const length = row.querySelector('.length-value').textContent;
                const b = row.querySelector('.b-value').value;
                const d = row.querySelector('.d-value').value;
                const qty = row.querySelector('.qty-value').textContent;

                data.push(['', description, no, length, b, d, qty, '']);
            });

            // Item 1 Total row
            const totalQty = document.getElementById('totalQty').textContent;
            const totalAmount = document.getElementById('totalAmount').textContent;
            data.push(['', 'TOTAL', '', '', '', '', totalQty + ' M³', 'Rs. ' + totalAmount]);

            // Item 1 Rate row
            const rateText = `Say ${totalQty} M³ @ Rs.${rates[1].toFixed(2)} per M³`;
            data.push(['', rateText, '', '', '', '', '', 'Rs. ' + totalAmount]);

            // Item 2 header
            data.push(['2.', 'Laterite masonary in cement mortar 1:6 for foundation including cost of materials, conveyance and labour charges, etc. complete.', '', '', '', '', '', '']);

            // Item 2 Below GL section
            data.push(['', 'BELOW GL', '', '', '', '', '', '']);
            document.querySelectorAll('.wall-row[data-item="2"][data-section="below_gl"]').forEach(row => {
                const description = row.querySelector('td:nth-child(1)').textContent.trim();
                const no = '1';
                const length = row.dataset.length;
                const b = row.querySelector('.b-value').value;
                const d = row.querySelector('.d-value').value;
                const qty = row.querySelector('.qty-value').textContent;

                data.push(['', description, no, length, b, d, qty, '']);
            });

            // Item 2 Basement section
            data.push(['', 'BASEMENT', '', '', '', '', '', '']);
            document.querySelectorAll('.wall-row[data-item="2"][data-section="basement"]').forEach(row => {
                const description = row.querySelector('td:nth-child(1)').textContent.trim();
                const no = '1';
                const length = row.dataset.length;
                const b = row.querySelector('.b-value').value;
                const d = row.querySelector('.d-value').value;
                const qty = row.querySelector('.qty-value').textContent;

                data.push(['', description, no, length, b, d, qty, '']);
            });

            // Item 2 Total row
            const item2TotalQty = document.getElementById('item2TotalQty').textContent;
            const item2TotalAmount = document.getElementById('item2TotalAmount').textContent;
            data.push(['', 'TOTAL', '', '', '', '', item2TotalQty + ' M³', 'Rs. ' + item2TotalAmount]);

            // Item 2 Rate row
            const item2RateText = `Say ${item2TotalQty} M³ @ Rs.${rates[2].toFixed(2)} per M³`;
            data.push(['', item2RateText, '', '', '', '', '', 'Rs. ' + item2TotalAmount]);

            // Convert to CSV format
            const csvContent = data.map(row =>
                row.map(cell => `"${cell}"`).join(',')
            ).join('\n');

            // Create and download file
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'CPWD_Estimate.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Initialize calculations on page load
        document.addEventListener('DOMContentLoaded', function() {
            calculateAll();
            calculateItem2Total();

            // Highlight rate input if edit_rates parameter is present
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('edit_rates') === '1') {
                const rateInput = document.getElementById('rateInput');
                rateInput.style.border = '2px solid #3b82f6';
                rateInput.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                rateInput.focus();
                rateInput.select();

                // Remove highlight after 3 seconds
                setTimeout(() => {
                    rateInput.style.border = '1px solid #d1d5db';
                    rateInput.style.boxShadow = 'none';
                }, 3000);
            }
        });

        // Print styles
        const printStyles = `
            @media print {
                body { background: white !important; }
                .container { max-width: none !important; }
                .controls, .nav-tabs, .btn, .item-controls { display: none !important; }
                .estimate-card { box-shadow: none !important; border: 1px solid #000 !important; }
                .estimate-table th, .estimate-table td { border: 1px solid #000 !important; }
            }
        `;
        
        const styleSheet = document.createElement("style");
        styleSheet.type = "text/css";
        styleSheet.innerText = printStyles;
        document.head.appendChild(styleSheet);
    </script>
</body>
</html>
