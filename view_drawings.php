<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Drawings - SmartEstimate</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f8fafc;
            color: #1a202c;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #1a202c;
            margin-bottom: 10px;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .drawing-viewer {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        .floor-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            border-bottom: 1px solid #e5e7eb;
        }

        .floor-tab {
            padding: 10px 20px;
            border: none;
            background: none;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            font-weight: 500;
            color: #6b7280;
        }

        .floor-tab.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }

        .drawing-content {
            min-height: 600px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .drawing-canvas {
            width: 100%;
            height: 600px;
            background: #f9fafb;
            position: relative;
        }

        .segment-overlay {
            position: absolute;
            background: rgba(59, 130, 246, 0.1);
            border: 2px solid #3b82f6;
            border-radius: 4px;
            pointer-events: none;
        }

        .segment-label {
            position: absolute;
            background: #3b82f6;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            transform: translate(-50%, -50%);
        }

        .legend {
            margin-top: 20px;
            padding: 15px;
            background: #f9fafb;
            border-radius: 8px;
        }

        .legend h3 {
            margin-bottom: 10px;
            color: #374151;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            border: 2px solid;
        }

        .wall-10cm { border-color: #ef4444; background: rgba(239, 68, 68, 0.1); }
        .wall-20cm { border-color: #10b981; background: rgba(16, 185, 129, 0.1); }
        .wall-23cm { border-color: #8b5cf6; background: rgba(139, 92, 246, 0.1); }

        .no-drawings {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }

        .no-drawings h3 {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Project Drawings</h1>
            <p>View floor plans with wall segments and measurements</p>
        </div>

        <div class="nav-buttons">
            <a href="index.php" class="btn btn-secondary">← Back to Projects</a>
            <?php if (isset($_GET['project'])): ?>
                <a href="cpwd_estimate.php?project=<?php echo htmlspecialchars($_GET['project']); ?>" class="btn btn-primary">View Estimate</a>
            <?php endif; ?>
        </div>

        <div class="drawing-viewer">
            <?php
            require_once 'project_manager.php';
            $projectManager = new ProjectManager();

            $project = null;
            $wallCalculations = [];

            if (isset($_GET['project'])) {
                $project = $projectManager->getProject($_GET['project']);
                if ($project && !empty($project['estimate_data'])) {
                    $resultData = $project['estimate_data'];
                    if (isset($resultData['data']['info']['wall_calculations'])) {
                        $wallCalculations = $resultData['data']['info']['wall_calculations'];
                    }
                }
            }

            if (!$project || empty($wallCalculations)):
            ?>
                <div class="no-drawings">
                    <h3>No Drawings Available</h3>
                    <p>Please upload a DXF file first to view drawings.</p>
                    <a href="index.php" class="btn btn-primary" style="margin-top: 15px;">Add New Project</a>
                </div>
            <?php else: ?>
                <div class="floor-tabs">
                    <button class="floor-tab active" onclick="showFloor('ground')">Ground Floor</button>
                    <button class="floor-tab" onclick="showFloor('first')">First Floor</button>
                </div>

                <div class="drawing-content">
                    <div id="ground-floor" class="drawing-canvas">
                        <canvas id="groundCanvas" width="1000" height="600"></canvas>
                    </div>
                    <div id="first-floor" class="drawing-canvas" style="display: none;">
                        <canvas id="firstCanvas" width="1000" height="600"></canvas>
                    </div>
                </div>

                <div class="legend">
                    <h3>Wall Types</h3>
                    <div class="legend-item">
                        <div class="legend-color wall-10cm"></div>
                        <span>WALLS 10CM (0.10m width)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color wall-20cm"></div>
                        <span>WALLS 20CM (0.20m width)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color wall-23cm"></div>
                        <span>WALLS 23CM (0.23m width)</span>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        const wallData = <?php echo json_encode($wallCalculations); ?>;
        
        function showFloor(floor) {
            // Update tabs
            document.querySelectorAll('.floor-tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
            
            // Show/hide floor canvases
            document.getElementById('ground-floor').style.display = floor === 'ground' ? 'block' : 'none';
            document.getElementById('first-floor').style.display = floor === 'first' ? 'block' : 'none';
            
            // Draw the selected floor
            if (floor === 'ground') {
                drawFloor('groundCanvas');
            } else {
                drawFloor('firstCanvas');
            }
        }
        
        function drawFloor(canvasId) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Set background
            ctx.fillStyle = '#f9fafb';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw walls
            let segmentIndex = 1;
            const colors = {
                'WALLS_10CM': '#ef4444',
                'WALLS_20CM': '#10b981',
                'WALLS_23CM': '#8b5cf6'
            };
            
            Object.keys(wallData).forEach(wallType => {
                const wallInfo = wallData[wallType];
                if (wallInfo.rectangles && wallInfo.rectangles.length > 0) {
                    ctx.strokeStyle = colors[wallType] || '#6b7280';
                    ctx.fillStyle = colors[wallType] + '20'; // Add transparency
                    ctx.lineWidth = 3;
                    
                    wallInfo.rectangles.forEach((rectangle, index) => {
                        if (rectangle.coordinates && rectangle.coordinates.length >= 3) {
                            drawWallSegment(ctx, rectangle, wallType, index + 1);
                        }
                    });
                }
            });
        }
        
        function drawWallSegment(ctx, rectangle, wallType, segmentNum) {
            const coords = rectangle.coordinates;
            if (!coords || coords.length < 3) return;
            
            // Scale and center the coordinates
            const scale = 8;
            const offsetX = 100;
            const offsetY = 100;
            
            // Draw the polygon
            ctx.beginPath();
            ctx.moveTo(coords[0][0] * scale + offsetX, coords[0][1] * scale + offsetY);
            
            for (let i = 1; i < coords.length; i++) {
                ctx.lineTo(coords[i][0] * scale + offsetX, coords[i][1] * scale + offsetY);
            }
            ctx.closePath();
            
            ctx.fill();
            ctx.stroke();
            
            // Calculate center point for label
            let centerX = 0, centerY = 0;
            coords.forEach(coord => {
                centerX += coord[0];
                centerY += coord[1];
            });
            centerX = (centerX / coords.length) * scale + offsetX;
            centerY = (centerY / coords.length) * scale + offsetY;
            
            // Draw segment label
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(centerX - 25, centerY - 10, 50, 20);
            ctx.strokeStyle = '#374151';
            ctx.lineWidth = 1;
            ctx.strokeRect(centerX - 25, centerY - 10, 50, 20);
            
            ctx.fillStyle = '#374151';
            ctx.font = '12px Inter, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(`S${segmentNum}`, centerX, centerY + 4);
            
            // Draw length label
            ctx.fillStyle = '#6b7280';
            ctx.font = '10px Inter, sans-serif';
            ctx.fillText(`${rectangle.length.toFixed(2)}m`, centerX, centerY + 25);
        }
        
        // Initialize with ground floor
        document.addEventListener('DOMContentLoaded', function() {
            if (Object.keys(wallData).length > 0) {
                drawFloor('groundCanvas');
            }
        });
    </script>
</body>
</html>
