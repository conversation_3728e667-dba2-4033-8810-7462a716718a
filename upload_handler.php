<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Configuration
define('UPLOAD_DIR', __DIR__ . '/uploads/');
define('MAX_FILE_SIZE', 50 * 1024 * 1024); // 50MB
define('ALLOWED_EXTENSIONS', ['dxf']);
define('PYTHON_SCRIPT', __DIR__ . '/process_dxf.py');

/**
 * Send JSON response and exit
 */
function sendResponse($success, $message, $data = null) {
    $response = [
        'success' => $success,
        'message' => $message
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response);
    exit;
}

/**
 * Log error messages
 */
function logError($message) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] ERROR: $message" . PHP_EOL;
    file_put_contents(__DIR__ . '/error.log', $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * Validate file upload
 */
function validateUpload($file) {
    // Check if file was uploaded
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        switch ($file['error']) {
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                return 'File size exceeds the maximum allowed limit.';
            case UPLOAD_ERR_PARTIAL:
                return 'File upload was interrupted.';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded.';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Temporary directory is missing.';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk.';
            case UPLOAD_ERR_EXTENSION:
                return 'File upload stopped by extension.';
            default:
                return 'Unknown upload error.';
        }
    }
    
    // Check file size
    if ($file['size'] > MAX_FILE_SIZE) {
        return 'File size exceeds the maximum allowed limit of ' . (MAX_FILE_SIZE / 1024 / 1024) . 'MB.';
    }
    
    // Check file extension
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($fileExtension, ALLOWED_EXTENSIONS)) {
        return 'Only DXF files are allowed.';
    }
    
    // Additional MIME type check
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    // DXF files are typically text/plain or application/octet-stream
    $allowedMimeTypes = ['text/plain', 'application/octet-stream', 'application/dxf'];
    if (!in_array($mimeType, $allowedMimeTypes)) {
        // Additional check: read first few bytes to verify DXF format
        $handle = fopen($file['tmp_name'], 'r');
        $firstLine = fgets($handle);
        fclose($handle);
        
        // DXF files typically start with "0" followed by "SECTION"
        if (trim($firstLine) !== '0') {
            return 'Invalid DXF file format.';
        }
    }
    
    return null; // No errors
}

/**
 * Generate secure filename
 */
function generateSecureFilename($originalName) {
    $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
    $basename = pathinfo($originalName, PATHINFO_FILENAME);
    
    // Sanitize basename
    $basename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $basename);
    $basename = substr($basename, 0, 50); // Limit length
    
    // Add timestamp and random string for uniqueness
    $timestamp = date('Y-m-d_H-i-s');
    $randomString = bin2hex(random_bytes(8));
    
    return $basename . '_' . $timestamp . '_' . $randomString . '.' . $extension;
}

/**
 * Create uploads directory if it doesn't exist
 */
function ensureUploadDirectory() {
    if (!is_dir(UPLOAD_DIR)) {
        if (!mkdir(UPLOAD_DIR, 0755, true)) {
            return false;
        }
        
        // Create .htaccess file to prevent direct access
        $htaccessContent = "Order Deny,Allow\nDeny from all\n";
        file_put_contents(UPLOAD_DIR . '.htaccess', $htaccessContent);
        
        // Create index.php to prevent directory listing
        $indexContent = "<?php\n// Access denied\nheader('HTTP/1.0 403 Forbidden');\nexit('Access denied');\n?>";
        file_put_contents(UPLOAD_DIR . 'index.php', $indexContent);
    }
    
    return true;
}

/**
 * Process DXF file with Python script
 */
function processDxfFile($filePath) {
    // Check if Python script exists
    if (!file_exists(PYTHON_SCRIPT)) {
        return [
            'success' => false,
            'error' => 'Python processing script not found.'
        ];
    }
    
    // Escape the file path for shell execution
    $escapedPath = escapeshellarg($filePath);
    $escapedScript = escapeshellarg(PYTHON_SCRIPT);
    
    // Execute Python script
    $command = "python3 $escapedScript $escapedPath 2>&1";
    $output = shell_exec($command);
    
    if ($output === null) {
        return [
            'success' => false,
            'error' => 'Failed to execute Python script.'
        ];
    }
    
    // Try to parse output as JSON (assuming Python script returns JSON)
    $result = json_decode($output, true);
    
    if (json_last_error() === JSON_ERROR_NONE) {
        return $result;
    } else {
        // If not JSON, return raw output
        return [
            'success' => true,
            'message' => 'File processed successfully.',
            'output' => trim($output)
        ];
    }
}

// Main execution
try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendResponse(false, 'Only POST requests are allowed.');
    }
    
    // Check if file was uploaded
    if (!isset($_FILES['dxf_file'])) {
        sendResponse(false, 'No file uploaded.');
    }
    
    $uploadedFile = $_FILES['dxf_file'];
    
    // Validate upload
    $validationError = validateUpload($uploadedFile);
    if ($validationError) {
        sendResponse(false, $validationError);
    }
    
    // Ensure upload directory exists
    if (!ensureUploadDirectory()) {
        logError('Failed to create upload directory');
        sendResponse(false, 'Server configuration error.');
    }
    
    // Generate secure filename
    $secureFilename = generateSecureFilename($uploadedFile['name']);
    $targetPath = UPLOAD_DIR . $secureFilename;
    
    // Move uploaded file
    if (!move_uploaded_file($uploadedFile['tmp_name'], $targetPath)) {
        logError('Failed to move uploaded file: ' . $uploadedFile['name']);
        sendResponse(false, 'Failed to save uploaded file.');
    }
    
    // Process the DXF file
    $processingResult = processDxfFile($targetPath);
    
    // Log the upload
    $logMessage = date('Y-m-d H:i:s') . " - File uploaded: {$uploadedFile['name']} -> $secureFilename" . PHP_EOL;
    file_put_contents(__DIR__ . '/upload.log', $logMessage, FILE_APPEND | LOCK_EX);
    
    // Return result
    if ($processingResult['success']) {
        sendResponse(true, 'File uploaded and processed successfully.', [
            'filename' => $secureFilename,
            'original_name' => $uploadedFile['name'],
            'processing_result' => $processingResult
        ]);
    } else {
        // Clean up uploaded file if processing failed
        if (file_exists($targetPath)) {
            unlink($targetPath);
        }
        
        sendResponse(false, 'File processing failed: ' . ($processingResult['error'] ?? 'Unknown error'));
    }
    
} catch (Exception $e) {
    logError('Exception in upload handler: ' . $e->getMessage());
    sendResponse(false, 'An unexpected error occurred.');
}
?>
